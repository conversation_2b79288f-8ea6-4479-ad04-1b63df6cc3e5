# ULTIMATE MASTER BITCOIN TRADING SYSTEM - Requirements
# =====================================================

# Core Data Science & Machine Learning
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
joblib>=1.0.0

# Visualization
matplotlib>=3.4.0

# Data Sources
requests>=2.25.0
yfinance>=0.1.70

# System Monitoring
psutil>=5.8.0

# GUI Framework (usually included with Python)
# tkinter - included with Python standard library

# Optional Packages (for enhanced functionality)
# ==============================================

# Deep Learning (LSTM Model)
tensorflow>=2.8.0
# Alternative for CPU-only:
# tensorflow-cpu>=2.8.0

# Enhanced Machine Learning
xgboost>=1.5.0

# Technical Analysis (advanced indicators)
# TA-Lib>=0.4.24
# Note: TA-Lib requires manual installation on Windows
# Download from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib

# GPU Monitoring
# GPUtil>=1.4.0
# Note: Only works with NVIDIA GPUs

# Windows Desktop Shortcuts (Windows only)
# pywin32>=227
# winshell>=0.6

# Development & Testing
# =====================

# Code Quality
# flake8>=4.0.0
# black>=22.0.0

# Testing
# pytest>=6.0.0
# pytest-cov>=3.0.0

# Documentation
# sphinx>=4.0.0

# Installation Instructions
# ========================
# 
# Basic Installation (required packages):
# pip install numpy pandas scikit-learn matplotlib requests yfinance psutil joblib
#
# Full Installation (with optional packages):
# pip install -r requirements.txt
#
# Manual TA-Lib Installation (Windows):
# 1. Download TA-Lib wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
# 2. pip install TA_Lib-0.4.24-cp39-cp39-win_amd64.whl (adjust for your Python version)
#
# TensorFlow Installation:
# - For GPU support: pip install tensorflow
# - For CPU only: pip install tensorflow-cpu
#
# System Requirements:
# - Python 3.8 or higher
# - Windows 10/11 (Linux/Mac experimental)
# - 4GB RAM minimum (8GB recommended)
# - Internet connection for Bitcoin data
# - Optional: NVIDIA GPU for LSTM training
