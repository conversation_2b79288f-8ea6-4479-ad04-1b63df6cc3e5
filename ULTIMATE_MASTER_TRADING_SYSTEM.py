#!/usr/bin/env python3
"""
🚀 ULTIMATE MASTER BITCOIN TRADING SYSTEM 🚀
============================================
🎯 DETAILREICHES TRADING TOOL MIT 3 HAUPTMODELLEN 🎯

✨ SYSTEM-FEATURES:
✅ 3 Hauptprognosemodelle (LSTM, Ensemble, Technical)
✅ Modernes Dashboard mit Live-Monitoring
✅ System-Monitoring (CPU/RAM/GPU)
✅ Kontinuierliches Training mit Intervall-Updates
✅ Interaktive Charts mit Crosshair & Zoom
✅ Separate Tabs mit Start/Stop-Kontrollen
✅ Detaillierte Fehlerausgabe & Logging
✅ Clean Process System
✅ Plugin-Architektur für Erweiterungen

🧠 PROGNOSEMODELLE:
├── 🔥 LSTM Deep Learning (Neuronale Netze)
├── 🏆 Champion Ensemble (RF + XGBoost + SVM)
└── 📈 Technical Analysis Master (50+ Indikatoren)

🎨 DASHBOARD-FEATURES:
├── 📊 Live Bitcoin Price Updates
├── 🎯 Crosshair-Funktionalität
├── 🔍 Zoom & Zeitbereich-Kontrollen
├── 📈 Separate Charts pro Modell
├── 🎮 Individual Start/Stop Controls
├── 📋 Training Logs & Progress
├── 💾 Daten-Persistierung
└── 🧹 Robustes Cleanup-System

⚙️ SYSTEM-MONITORING:
├── 💻 CPU-Auslastung Live
├── 🧠 RAM-Verbrauch Tracking
├── 🎮 GPU-Monitoring (falls verfügbar)
├── 🔄 Thread-Management
├── ⚡ Performance-Metriken
└── 🚨 Fehler-Überwachung

Version: ULTIMATE_MASTER 1.0
Erstellt: 2025-07-03
Basiert auf: Beste Features aus allen vorhandenen Scripten
"""

import sys
import os
import threading
import time
import logging
import json
import sqlite3
import warnings
import atexit
import signal
import psutil
import gc
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import traceback

# GUI Framework
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import tkinter.font as tkFont

# Data Science & ML
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from matplotlib.animation import FuncAnimation

# Machine Learning
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.model_selection import cross_val_score, TimeSeriesSplit
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import accuracy_score, mean_squared_error
import joblib

# Deep Learning (TensorFlow/Keras)
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False
    print("⚠️ TensorFlow nicht verfügbar - LSTM-Modell deaktiviert")

# XGBoost (falls verfügbar)
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    from sklearn.ensemble import GradientBoostingRegressor
    XGBOOST_AVAILABLE = False

# Technical Analysis
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ TA-Lib nicht verfügbar - Vereinfachte technische Indikatoren")

# Data Sources
import requests
import yfinance as yf

# Suppress warnings
warnings.filterwarnings('ignore')
plt.style.use('dark_background')
np.random.seed(42)

# ============================================================================
# SYSTEM CONFIGURATION & ENUMS
# ============================================================================

class ModelType(Enum):
    """Modell-Typen"""
    LSTM_DEEP_LEARNING = "lstm_dl"
    CHAMPION_ENSEMBLE = "champion_ensemble"
    TECHNICAL_ANALYSIS = "technical_analysis"

class SystemStatus(Enum):
    """System-Status"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    TRAINING = "training"
    PAUSED = "paused"
    ERROR = "error"
    STOPPING = "stopping"
    STOPPED = "stopped"

@dataclass
class SystemConfig:
    """System-Konfiguration"""
    # Data Settings
    max_data_points: int = 1000
    data_update_interval: int = 30  # seconds
    cache_duration: int = 300  # seconds
    
    # Training Settings
    training_intervals: Dict[str, int] = field(default_factory=lambda: {
        'lstm': 300,      # 5 minutes
        'ensemble': 180,  # 3 minutes  
        'technical': 60   # 1 minute
    })
    
    # Performance Settings
    max_threads: int = 6
    chart_update_throttle: float = 0.1
    memory_cleanup_interval: int = 600  # 10 minutes
    
    # GUI Settings
    window_width: int = 1920
    window_height: int = 1080
    chart_height: int = 400
    
    # Monitoring Settings
    system_monitor_interval: int = 5  # seconds
    performance_history_size: int = 100

@dataclass
class PredictionResult:
    """Standardisiertes Vorhersage-Ergebnis"""
    model_name: str
    timestamp: datetime
    current_price: float
    predicted_price_24h: float
    predicted_price_48h: float
    confidence: float
    direction: str  # "UP", "DOWN", "SIDEWAYS"
    probability_up: float
    probability_down: float
    risk_level: str  # "LOW", "MEDIUM", "HIGH"
    features_used: List[str]
    execution_time_ms: float
    metadata: Dict[str, Any] = field(default_factory=dict)

# ============================================================================
# GLOBAL CLEANUP & RESOURCE MANAGEMENT
# ============================================================================

class GlobalResourceManager:
    """Globaler Resource Manager für sauberes Cleanup"""
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.active_systems = []
            self.cleanup_registered = False
            self.shutdown_in_progress = False
            self._register_cleanup_handlers()
            GlobalResourceManager._initialized = True
            logging.info("🔧 Global Resource Manager initialisiert")
    
    def _register_cleanup_handlers(self):
        """Registriere Cleanup-Handler"""
        if not self.cleanup_registered:
            atexit.register(self.emergency_cleanup)
            try:
                signal.signal(signal.SIGINT, self._signal_handler)
                signal.signal(signal.SIGTERM, self._signal_handler)
            except Exception as e:
                logging.warning(f"Signal handler registration failed: {e}")
            self.cleanup_registered = True
    
    def _signal_handler(self, signum, frame):
        """Signal-Handler für sauberes Beenden"""
        print(f"\n🚨 Signal {signum} empfangen - Starte Emergency Cleanup...")
        self.emergency_cleanup()
        sys.exit(0)
    
    def register_system(self, system):
        """Registriere System für Cleanup"""
        if system not in self.active_systems:
            self.active_systems.append(system)
            logging.info(f"✅ System registriert: {type(system).__name__}")
    
    def unregister_system(self, system):
        """Entferne System von Cleanup"""
        if system in self.active_systems:
            self.active_systems.remove(system)
            logging.info(f"❌ System entfernt: {type(system).__name__}")
    
    def emergency_cleanup(self):
        """Emergency Cleanup aller Systeme"""
        if self.shutdown_in_progress:
            return
        
        self.shutdown_in_progress = True
        print("🧹 Emergency Cleanup gestartet...")
        
        # Cleanup aller registrierten Systeme
        for system in self.active_systems[:]:
            try:
                if hasattr(system, 'force_cleanup'):
                    system.force_cleanup()
                elif hasattr(system, 'cleanup'):
                    system.cleanup()
                logging.info(f"✅ System cleanup: {type(system).__name__}")
            except Exception as e:
                logging.error(f"❌ Cleanup Fehler für {type(system).__name__}: {e}")
        
        # Matplotlib cleanup
        try:
            plt.close('all')
            logging.info("✅ Matplotlib cleanup")
        except Exception as e:
            logging.error(f"❌ Matplotlib cleanup Fehler: {e}")
        
        # Memory cleanup
        try:
            gc.collect()
            logging.info("✅ Memory cleanup")
        except Exception as e:
            logging.error(f"❌ Memory cleanup Fehler: {e}")
        
        print("✅ Emergency Cleanup abgeschlossen")

# ============================================================================
# LOGGING SETUP
# ============================================================================

def setup_logging():
    """Setup detailliertes Logging System"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Log-Datei mit Timestamp
    log_file = log_dir / f"trading_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

    # Custom Formatter ohne Emojis für Terminal
    class SafeFormatter(logging.Formatter):
        def format(self, record):
            # Entferne Emojis für Terminal-Output
            msg = super().format(record)
            # Ersetze häufige Emojis mit Text
            emoji_replacements = {
                '🚀': '[START]',
                '✅': '[OK]',
                '❌': '[ERROR]',
                '⚠️': '[WARNING]',
                '🔧': '[SETUP]',
                '🔍': '[MONITOR]',
                '🎨': '[GUI]',
                '📊': '[PREDICTION]',
                '🔥': '[LSTM]',
                '🏆': '[ENSEMBLE]',
                '📈': '[TECHNICAL]',
                '🔄': '[TRAINING]',
                '💻': '[SYSTEM]',
                '🧠': '[RAM]',
                '🎮': '[GPU]',
                '📋': '[LOG]',
                '🎯': '[TARGET]',
                '⚡': '[PERFORMANCE]',
                '🚨': '[ALERT]',
                '🧹': '[CLEANUP]'
            }
            for emoji, replacement in emoji_replacements.items():
                msg = msg.replace(emoji, replacement)
            return msg

    # File Handler (mit Emojis)
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

    # Console Handler (ohne Emojis)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(SafeFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))

    # Root Logger konfigurieren
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Spezielle Logger für verschiedene Komponenten
    loggers = {
        'system': logging.getLogger('system'),
        'data': logging.getLogger('data'),
        'models': logging.getLogger('models'),
        'gui': logging.getLogger('gui'),
        'monitoring': logging.getLogger('monitoring')
    }
    
    return loggers

# Initialize logging
LOGGERS = setup_logging()

# ============================================================================
# SYSTEM MONITORING
# ============================================================================

class SystemMonitor:
    """System-Monitoring für CPU, RAM, GPU"""

    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.stats_history = []
        self.max_history = 100
        self.callbacks = []

        # GPU Monitoring (falls verfügbar)
        self.gpu_available = False
        try:
            import GPUtil
            self.gpu_available = True
            self.gpu_util = GPUtil
        except ImportError:
            pass

    def add_callback(self, callback):
        """Füge Callback für Stats-Updates hinzu"""
        self.callbacks.append(callback)

    def start_monitoring(self):
        """Starte System-Monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            LOGGERS['monitoring'].info("🔍 System-Monitoring gestartet")

    def stop_monitoring(self):
        """Stoppe System-Monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        LOGGERS['monitoring'].info("⏹️ System-Monitoring gestoppt")

    def _monitor_loop(self):
        """Monitoring-Loop"""
        while self.monitoring:
            try:
                stats = self._collect_system_stats()
                self.stats_history.append(stats)

                # History begrenzen
                if len(self.stats_history) > self.max_history:
                    self.stats_history.pop(0)

                # Callbacks benachrichtigen
                for callback in self.callbacks:
                    try:
                        callback(stats)
                    except Exception as e:
                        LOGGERS['monitoring'].error(f"Monitor callback error: {e}")

                time.sleep(5)  # 5 Sekunden Intervall

            except Exception as e:
                LOGGERS['monitoring'].error(f"System monitoring error: {e}")
                time.sleep(10)

    def _collect_system_stats(self) -> Dict[str, Any]:
        """Sammle System-Statistiken"""
        stats = {
            'timestamp': datetime.now(),
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'memory_used_gb': psutil.virtual_memory().used / (1024**3),
            'memory_total_gb': psutil.virtual_memory().total / (1024**3),
            'disk_usage_percent': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
            'active_threads': threading.active_count(),
            'gpu_stats': None
        }

        # GPU Stats (falls verfügbar)
        if self.gpu_available:
            try:
                gpus = self.gpu_util.getGPUs()
                if gpus:
                    gpu = gpus[0]  # Erste GPU
                    stats['gpu_stats'] = {
                        'gpu_percent': gpu.load * 100,
                        'gpu_memory_percent': gpu.memoryUtil * 100,
                        'gpu_temperature': gpu.temperature
                    }
            except Exception as e:
                LOGGERS['monitoring'].warning(f"GPU monitoring error: {e}")

        return stats

    def get_latest_stats(self) -> Optional[Dict[str, Any]]:
        """Hole neueste Statistiken"""
        return self.stats_history[-1] if self.stats_history else None

    def get_stats_history(self) -> List[Dict[str, Any]]:
        """Hole Statistik-Historie"""
        return self.stats_history.copy()

# ============================================================================
# DATA PROVIDER - ECHTZEIT BITCOIN DATEN
# ============================================================================

class RealTimeDataProvider:
    """Echtzeit Bitcoin Daten Provider"""

    def __init__(self):
        self.data_cache = pd.DataFrame()
        self.last_update = None
        self.update_interval = 30  # seconds
        self.max_cache_size = 1000
        self.data_sources = ['binance', 'coinbase', 'yfinance']
        self.current_price = 0.0
        self.price_history = []

    def get_current_price(self) -> float:
        """Hole aktuellen Bitcoin Preis"""
        try:
            # Binance API
            response = requests.get(
                'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT',
                timeout=5
            )
            if response.status_code == 200:
                data = response.json()
                price = float(data['price'])
                self.current_price = price
                return price
        except Exception as e:
            LOGGERS['data'].warning(f"Binance API error: {e}")

        # Fallback: Yahoo Finance
        try:
            ticker = yf.Ticker("BTC-USD")
            data = ticker.history(period="1d", interval="1m")
            if not data.empty:
                price = float(data['Close'].iloc[-1])
                self.current_price = price
                return price
        except Exception as e:
            LOGGERS['data'].warning(f"Yahoo Finance error: {e}")

        return self.current_price

    def get_historical_data(self, hours: int = 24) -> pd.DataFrame:
        """Hole historische Daten"""
        try:
            # Yahoo Finance für historische Daten
            ticker = yf.Ticker("BTC-USD")

            # Bestimme Periode und Intervall basierend auf Stunden
            if hours <= 1:
                period = "1d"
                interval = "1m"
            elif hours <= 24:
                period = "5d"
                interval = "5m"
            elif hours <= 168:  # 1 Woche
                period = "1mo"
                interval = "1h"
            else:
                period = "3mo"
                interval = "1d"

            data = ticker.history(period=period, interval=interval)

            if not data.empty:
                # Nur die letzten X Stunden
                end_time = data.index[-1]
                start_time = end_time - timedelta(hours=hours)
                data = data[data.index >= start_time]

                # Cache aktualisieren
                self.data_cache = data
                self.last_update = datetime.now()

                return data

        except Exception as e:
            LOGGERS['data'].error(f"Historical data error: {e}")

        return pd.DataFrame()

    def update_price_history(self):
        """Aktualisiere Preis-Historie"""
        current_price = self.get_current_price()
        if current_price > 0:
            self.price_history.append({
                'timestamp': datetime.now(),
                'price': current_price
            })

            # Historie begrenzen
            if len(self.price_history) > self.max_cache_size:
                self.price_history.pop(0)

    def get_price_history_df(self) -> pd.DataFrame:
        """Hole Preis-Historie als DataFrame"""
        if self.price_history:
            df = pd.DataFrame(self.price_history)
            df.set_index('timestamp', inplace=True)
            return df
        return pd.DataFrame()

# ============================================================================
# FEATURE ENGINEERING
# ============================================================================

class FeatureEngineer:
    """Advanced Feature Engineering für Bitcoin Trading"""

    def __init__(self):
        self.feature_cache = {}
        self.scaler = StandardScaler()
        self.fitted = False

    def engineer_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Erstelle erweiterte Features"""
        if df.empty:
            return df

        try:
            # Kopie erstellen
            features_df = df.copy()

            # Basis-Features
            features_df = self._add_price_features(features_df)
            features_df = self._add_volume_features(features_df)
            features_df = self._add_technical_indicators(features_df)
            features_df = self._add_time_features(features_df)
            features_df = self._add_statistical_features(features_df)

            # NaN-Werte behandeln
            features_df = features_df.fillna(method='ffill').fillna(method='bfill')

            return features_df

        except Exception as e:
            LOGGERS['data'].error(f"Feature engineering error: {e}")
            return df

    def _add_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preis-basierte Features"""
        # Returns
        df['returns_1h'] = df['Close'].pct_change(1)
        df['returns_4h'] = df['Close'].pct_change(4)
        df['returns_24h'] = df['Close'].pct_change(24)

        # Price ratios
        df['high_low_ratio'] = df['High'] / df['Low']
        df['close_open_ratio'] = df['Close'] / df['Open']

        # Price position in range
        df['price_position'] = (df['Close'] - df['Low']) / (df['High'] - df['Low'])

        return df

    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Volume-basierte Features"""
        if 'Volume' in df.columns:
            # Volume moving averages
            df['volume_ma_5'] = df['Volume'].rolling(5).mean()
            df['volume_ma_20'] = df['Volume'].rolling(20).mean()

            # Volume ratios
            df['volume_ratio'] = df['Volume'] / df['volume_ma_20']

            # Price-Volume features
            df['price_volume'] = df['Close'] * df['Volume']

        return df

    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Technische Indikatoren"""
        # Moving Averages
        for period in [5, 10, 20, 50]:
            df[f'sma_{period}'] = df['Close'].rolling(period).mean()
            df[f'ema_{period}'] = df['Close'].ewm(span=period).mean()

        # RSI
        df['rsi_14'] = self._calculate_rsi(df['Close'], 14)

        # MACD
        df = self._add_macd(df)

        # Bollinger Bands
        df = self._add_bollinger_bands(df)

        # ATR (Average True Range)
        df['atr_14'] = self._calculate_atr(df, 14)

        return df

    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Zeit-basierte Features"""
        df['hour'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['month'] = df.index.month

        # Cyclical encoding
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['dow_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['dow_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)

        return df

    def _add_statistical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Statistische Features"""
        # Rolling statistics
        for window in [5, 10, 20]:
            df[f'volatility_{window}'] = df['Close'].rolling(window).std()
            df[f'skew_{window}'] = df['Close'].rolling(window).skew()
            df[f'kurt_{window}'] = df['Close'].rolling(window).kurt()

        return df

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Berechne RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _add_macd(self, df: pd.DataFrame) -> pd.DataFrame:
        """Füge MACD hinzu"""
        exp1 = df['Close'].ewm(span=12).mean()
        exp2 = df['Close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        return df

    def _add_bollinger_bands(self, df: pd.DataFrame, period: int = 20, std: float = 2) -> pd.DataFrame:
        """Füge Bollinger Bands hinzu"""
        sma = df['Close'].rolling(period).mean()
        std_dev = df['Close'].rolling(period).std()
        df['bb_upper'] = sma + (std_dev * std)
        df['bb_lower'] = sma - (std_dev * std)
        df['bb_middle'] = sma
        df['bb_width'] = df['bb_upper'] - df['bb_lower']
        df['bb_position'] = (df['Close'] - df['bb_lower']) / df['bb_width']
        return df

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> pd.Series:
        """Berechne Average True Range"""
        high_low = df['High'] - df['Low']
        high_close = np.abs(df['High'] - df['Close'].shift())
        low_close = np.abs(df['Low'] - df['Close'].shift())
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.rolling(period).mean()
        return atr

# ============================================================================
# PROGNOSEMODELL 1: LSTM DEEP LEARNING
# ============================================================================

class LSTMDeepLearningModel:
    """🔥 LSTM Deep Learning Model für Bitcoin Prognosen"""

    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.is_trained = False
        self.training_history = []
        self.sequence_length = 60
        self.features_to_use = [
            'Close', 'Volume', 'rsi_14', 'macd', 'bb_position',
            'sma_20', 'ema_20', 'volatility_20', 'returns_1h'
        ]

    def prepare_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Bereite Daten für LSTM vor"""
        if not TF_AVAILABLE:
            raise Exception("TensorFlow nicht verfügbar")

        # Features auswählen
        available_features = [f for f in self.features_to_use if f in df.columns]
        if len(available_features) < 3:
            raise Exception("Nicht genügend Features verfügbar")

        data = df[available_features].values

        # Skalierung
        if not self.is_trained:
            data_scaled = self.scaler.fit_transform(data)
        else:
            data_scaled = self.scaler.transform(data)

        # Sequenzen erstellen
        X, y = [], []
        for i in range(self.sequence_length, len(data_scaled)):
            X.append(data_scaled[i-self.sequence_length:i])
            y.append(data_scaled[i, 0])  # Close price

        return np.array(X), np.array(y)

    def build_model(self, input_shape: Tuple[int, int]) -> tf.keras.Model:
        """Baue LSTM-Modell"""
        model = Sequential([
            LSTM(128, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            LSTM(64, return_sequences=True),
            Dropout(0.2),
            LSTM(32, return_sequences=False),
            Dropout(0.2),
            BatchNormalization(),
            Dense(25, activation='relu'),
            Dense(1)
        ])

        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )

        return model

    def train(self, df: pd.DataFrame) -> bool:
        """Trainiere LSTM-Modell"""
        try:
            if not TF_AVAILABLE:
                LOGGERS['models'].warning("TensorFlow nicht verfügbar - LSTM Training übersprungen")
                return False

            LOGGERS['models'].info("🔥 Starte LSTM Training...")
            start_time = time.time()

            # Daten vorbereiten
            X, y = self.prepare_data(df)

            if len(X) < 100:
                LOGGERS['models'].warning("Nicht genügend Daten für LSTM Training")
                return False

            # Train/Test Split
            split_idx = int(len(X) * 0.8)
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]

            # Modell bauen
            if self.model is None:
                self.model = self.build_model((X.shape[1], X.shape[2]))

            # Training mit Early Stopping
            early_stopping = EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True
            )

            history = self.model.fit(
                X_train, y_train,
                epochs=100,
                batch_size=32,
                validation_data=(X_test, y_test),
                callbacks=[early_stopping],
                verbose=0
            )

            # Performance bewerten
            train_loss = history.history['loss'][-1]
            val_loss = history.history['val_loss'][-1]

            self.training_history.append({
                'timestamp': datetime.now(),
                'train_loss': train_loss,
                'val_loss': val_loss,
                'epochs': len(history.history['loss']),
                'training_time': time.time() - start_time
            })

            self.is_trained = True

            LOGGERS['models'].info(f"✅ LSTM Training abgeschlossen - Val Loss: {val_loss:.6f}")
            return True

        except Exception as e:
            LOGGERS['models'].error(f"❌ LSTM Training Fehler: {e}")
            return False

    def predict(self, df: pd.DataFrame) -> Optional[PredictionResult]:
        """Erstelle LSTM Prognose"""
        try:
            if not self.is_trained or self.model is None:
                return None

            start_time = time.time()

            # Daten vorbereiten
            X, _ = self.prepare_data(df)

            if len(X) == 0:
                return None

            # Vorhersage für die nächsten 24h und 48h
            last_sequence = X[-1:]

            # 24h Vorhersage (24 Schritte)
            pred_24h = []
            current_seq = last_sequence.copy()

            for _ in range(24):
                next_pred = self.model.predict(current_seq, verbose=0)[0, 0]
                pred_24h.append(next_pred)

                # Sequenz aktualisieren
                new_row = current_seq[0, -1:].copy()
                new_row[0, 0] = next_pred  # Close price
                current_seq = np.concatenate([current_seq[:, 1:], new_row[:, None]], axis=1)

            # 48h Vorhersage
            pred_48h = []
            for _ in range(24):  # Weitere 24 Schritte
                next_pred = self.model.predict(current_seq, verbose=0)[0, 0]
                pred_48h.append(next_pred)

                new_row = current_seq[0, -1:].copy()
                new_row[0, 0] = next_pred
                current_seq = np.concatenate([current_seq[:, 1:], new_row[:, None]], axis=1)

            # Rücktransformation
            dummy_array = np.zeros((1, len(self.scaler.scale_)))
            dummy_array[0, 0] = np.mean(pred_24h)
            price_24h = self.scaler.inverse_transform(dummy_array)[0, 0]

            dummy_array[0, 0] = np.mean(pred_48h)
            price_48h = self.scaler.inverse_transform(dummy_array)[0, 0]

            current_price = df['Close'].iloc[-1]

            # Richtung und Konfidenz bestimmen
            direction_24h = "UP" if price_24h > current_price else "DOWN"
            direction_48h = "UP" if price_48h > current_price else "DOWN"

            # Konfidenz basierend auf Volatilität der Vorhersagen
            volatility_24h = np.std(pred_24h)
            confidence = max(0.1, min(0.95, 1.0 - volatility_24h))

            # Wahrscheinlichkeiten
            prob_up = confidence if direction_24h == "UP" else 1 - confidence
            prob_down = 1 - prob_up

            execution_time = (time.time() - start_time) * 1000

            return PredictionResult(
                model_name="LSTM Deep Learning",
                timestamp=datetime.now(),
                current_price=current_price,
                predicted_price_24h=price_24h,
                predicted_price_48h=price_48h,
                confidence=confidence,
                direction=direction_24h,
                probability_up=prob_up,
                probability_down=prob_down,
                risk_level="MEDIUM",
                features_used=self.features_to_use,
                execution_time_ms=execution_time,
                metadata={
                    'volatility_24h': volatility_24h,
                    'sequence_length': self.sequence_length,
                    'model_type': 'LSTM'
                }
            )

        except Exception as e:
            LOGGERS['models'].error(f"❌ LSTM Prediction Fehler: {e}")
            return None

# ============================================================================
# PROGNOSEMODELL 2: CHAMPION ENSEMBLE
# ============================================================================

class ChampionEnsembleModel:
    """🏆 Champion Ensemble Model mit Random Forest, XGBoost, SVM"""

    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.is_trained = False
        self.model_weights = {'rf': 0.4, 'xgb': 0.35, 'svm': 0.25}
        self.performance_history = []
        self.features_to_use = [
            'Close', 'Volume', 'rsi_14', 'macd', 'macd_signal', 'bb_position',
            'sma_5', 'sma_10', 'sma_20', 'ema_5', 'ema_10', 'ema_20',
            'volatility_5', 'volatility_10', 'volatility_20',
            'returns_1h', 'returns_4h', 'high_low_ratio', 'close_open_ratio',
            'hour_sin', 'hour_cos', 'dow_sin', 'dow_cos'
        ]

        self._initialize_models()

    def _initialize_models(self):
        """Initialisiere Ensemble-Modelle"""
        # Random Forest
        self.models['rf'] = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )

        # XGBoost oder Gradient Boosting
        if XGBOOST_AVAILABLE:
            self.models['xgb'] = xgb.XGBRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            )
        else:
            self.models['xgb'] = GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            )

        # SVM (für kleinere Datasets)
        from sklearn.svm import SVR
        self.models['svm'] = SVR(
            kernel='rbf',
            C=1.0,
            gamma='scale'
        )

        # Scaler für jedes Modell
        for model_name in self.models.keys():
            self.scalers[model_name] = RobustScaler()

    def prepare_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Bereite Features für Ensemble vor"""
        # Verfügbare Features auswählen
        available_features = [f for f in self.features_to_use if f in df.columns]

        if len(available_features) < 10:
            LOGGERS['models'].warning(f"Nur {len(available_features)} Features verfügbar für Ensemble")

        features_df = df[available_features].copy()

        # Zusätzliche abgeleitete Features
        if 'Close' in features_df.columns:
            # Lag features
            for lag in [1, 2, 3, 5]:
                features_df[f'close_lag_{lag}'] = features_df['Close'].shift(lag)

            # Rolling features
            for window in [5, 10, 20]:
                features_df[f'close_rolling_mean_{window}'] = features_df['Close'].rolling(window).mean()
                features_df[f'close_rolling_std_{window}'] = features_df['Close'].rolling(window).std()

        # NaN-Werte behandeln
        features_df = features_df.fillna(method='ffill').fillna(method='bfill')

        return features_df

    def train(self, df: pd.DataFrame) -> bool:
        """Trainiere Ensemble-Modelle"""
        try:
            LOGGERS['models'].info("🏆 Starte Champion Ensemble Training...")
            start_time = time.time()

            # Features vorbereiten
            features_df = self.prepare_features(df)

            if len(features_df) < 50:
                LOGGERS['models'].warning("Nicht genügend Daten für Ensemble Training")
                return False

            # Target: Nächste Stunde Preis-Änderung
            target = df['Close'].shift(-1) / df['Close'] - 1  # Returns

            # Gültige Daten
            valid_idx = ~(target.isna() | features_df.isna().any(axis=1))
            X = features_df[valid_idx]
            y = target[valid_idx]

            if len(X) < 30:
                LOGGERS['models'].warning("Nicht genügend gültige Daten für Training")
                return False

            # Train/Test Split
            split_idx = int(len(X) * 0.8)
            X_train, X_test = X.iloc[:split_idx], X.iloc[split_idx:]
            y_train, y_test = y.iloc[:split_idx], y.iloc[split_idx:]

            trained_models = 0
            model_scores = {}

            # Trainiere jedes Modell
            for name, model in self.models.items():
                try:
                    # Skalierung
                    X_train_scaled = self.scalers[name].fit_transform(X_train)
                    X_test_scaled = self.scalers[name].transform(X_test)

                    # Training
                    model.fit(X_train_scaled, y_train)

                    # Bewertung
                    train_score = model.score(X_train_scaled, y_train)
                    test_score = model.score(X_test_scaled, y_test)

                    model_scores[name] = {
                        'train_score': train_score,
                        'test_score': test_score,
                        'features_count': X_train.shape[1]
                    }

                    trained_models += 1
                    LOGGERS['models'].info(f"✅ {name.upper()} trainiert - Test Score: {test_score:.4f}")

                except Exception as e:
                    LOGGERS['models'].error(f"❌ {name.upper()} Training Fehler: {e}")

            if trained_models > 0:
                self.is_trained = True

                # Performance-Historie aktualisieren
                self.performance_history.append({
                    'timestamp': datetime.now(),
                    'trained_models': trained_models,
                    'model_scores': model_scores,
                    'training_time': time.time() - start_time,
                    'data_points': len(X)
                })

                LOGGERS['models'].info(f"✅ Champion Ensemble Training abgeschlossen - {trained_models} Modelle")
                return True
            else:
                LOGGERS['models'].error("❌ Kein Modell erfolgreich trainiert")
                return False

        except Exception as e:
            LOGGERS['models'].error(f"❌ Champion Ensemble Training Fehler: {e}")
            return False

    def predict(self, df: pd.DataFrame) -> Optional[PredictionResult]:
        """Erstelle Ensemble Prognose"""
        try:
            if not self.is_trained:
                return None

            start_time = time.time()

            # Features vorbereiten
            features_df = self.prepare_features(df)

            if features_df.empty:
                return None

            # Letzte Zeile für Vorhersage
            X_current = features_df.iloc[-1:].values

            predictions = {}
            confidences = {}

            # Vorhersagen von allen Modellen
            for name, model in self.models.items():
                try:
                    X_scaled = self.scalers[name].transform(X_current)
                    pred = model.predict(X_scaled)[0]
                    predictions[name] = pred

                    # Konfidenz basierend auf Feature Importance (falls verfügbar)
                    if hasattr(model, 'feature_importances_'):
                        confidences[name] = np.mean(model.feature_importances_)
                    else:
                        confidences[name] = 0.5

                except Exception as e:
                    LOGGERS['models'].warning(f"Prediction error for {name}: {e}")

            if not predictions:
                return None

            # Gewichtete Ensemble-Vorhersage
            ensemble_pred = 0
            total_weight = 0

            for name, pred in predictions.items():
                weight = self.model_weights.get(name, 0.33)
                ensemble_pred += pred * weight
                total_weight += weight

            if total_weight > 0:
                ensemble_pred /= total_weight

            # Aktuelle und vorhergesagte Preise
            current_price = df['Close'].iloc[-1]
            predicted_price_24h = current_price * (1 + ensemble_pred)
            predicted_price_48h = current_price * (1 + ensemble_pred * 1.5)  # Extrapolation

            # Richtung und Konfidenz
            direction = "UP" if ensemble_pred > 0 else "DOWN"
            confidence = min(0.95, max(0.1, abs(ensemble_pred) * 10))  # Skalierte Konfidenz

            prob_up = confidence if direction == "UP" else 1 - confidence
            prob_down = 1 - prob_up

            # Risk Level basierend auf Volatilität
            volatility = df['Close'].rolling(20).std().iloc[-1] / current_price
            if volatility < 0.02:
                risk_level = "LOW"
            elif volatility < 0.05:
                risk_level = "MEDIUM"
            else:
                risk_level = "HIGH"

            execution_time = (time.time() - start_time) * 1000

            return PredictionResult(
                model_name="Champion Ensemble",
                timestamp=datetime.now(),
                current_price=current_price,
                predicted_price_24h=predicted_price_24h,
                predicted_price_48h=predicted_price_48h,
                confidence=confidence,
                direction=direction,
                probability_up=prob_up,
                probability_down=prob_down,
                risk_level=risk_level,
                features_used=list(features_df.columns),
                execution_time_ms=execution_time,
                metadata={
                    'ensemble_prediction': ensemble_pred,
                    'individual_predictions': predictions,
                    'model_weights': self.model_weights,
                    'volatility': volatility
                }
            )

        except Exception as e:
            LOGGERS['models'].error(f"❌ Champion Ensemble Prediction Fehler: {e}")
            return None

# ============================================================================
# PROGNOSEMODELL 3: TECHNICAL ANALYSIS MASTER
# ============================================================================

class TechnicalAnalysisMaster:
    """📈 Technical Analysis Master mit 50+ Indikatoren"""

    def __init__(self):
        self.indicators = {}
        self.weights = {}
        self.is_trained = False
        self.signal_history = []
        self.performance_metrics = {}

        # Indikator-Gewichtungen (basierend auf Erfahrung)
        self.default_weights = {
            'rsi': 0.15,
            'macd': 0.15,
            'bollinger': 0.12,
            'moving_averages': 0.12,
            'volume': 0.10,
            'momentum': 0.10,
            'volatility': 0.08,
            'support_resistance': 0.08,
            'trend': 0.10
        }

        self.weights = self.default_weights.copy()

    def calculate_all_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Berechne alle technischen Indikatoren"""
        indicators = {}

        try:
            # RSI Signale
            indicators['rsi'] = self._analyze_rsi(df)

            # MACD Signale
            indicators['macd'] = self._analyze_macd(df)

            # Bollinger Bands
            indicators['bollinger'] = self._analyze_bollinger_bands(df)

            # Moving Averages
            indicators['moving_averages'] = self._analyze_moving_averages(df)

            # Volume Analyse
            indicators['volume'] = self._analyze_volume(df)

            # Momentum Indikatoren
            indicators['momentum'] = self._analyze_momentum(df)

            # Volatilität
            indicators['volatility'] = self._analyze_volatility(df)

            # Support/Resistance
            indicators['support_resistance'] = self._analyze_support_resistance(df)

            # Trend Analyse
            indicators['trend'] = self._analyze_trend(df)

        except Exception as e:
            LOGGERS['models'].error(f"Indicator calculation error: {e}")

        return indicators

    def _analyze_rsi(self, df: pd.DataFrame) -> Dict[str, Any]:
        """RSI Analyse"""
        if 'rsi_14' not in df.columns:
            return {'signal': 0, 'strength': 0, 'description': 'RSI nicht verfügbar'}

        rsi = df['rsi_14'].iloc[-1]

        if rsi > 70:
            signal = -1  # Überkauft
            strength = min(1.0, (rsi - 70) / 20)
            description = f"Überkauft (RSI: {rsi:.1f})"
        elif rsi < 30:
            signal = 1   # Überverkauft
            strength = min(1.0, (30 - rsi) / 20)
            description = f"Überverkauft (RSI: {rsi:.1f})"
        else:
            signal = 0   # Neutral
            strength = 0.5
            description = f"Neutral (RSI: {rsi:.1f})"

        return {
            'signal': signal,
            'strength': strength,
            'value': rsi,
            'description': description
        }

    def _analyze_macd(self, df: pd.DataFrame) -> Dict[str, Any]:
        """MACD Analyse"""
        if 'macd' not in df.columns or 'macd_signal' not in df.columns:
            return {'signal': 0, 'strength': 0, 'description': 'MACD nicht verfügbar'}

        macd = df['macd'].iloc[-1]
        macd_signal = df['macd_signal'].iloc[-1]
        macd_hist = df['macd_histogram'].iloc[-1] if 'macd_histogram' in df.columns else macd - macd_signal

        # Signal basierend auf MACD Crossover
        if macd > macd_signal and macd_hist > 0:
            signal = 1   # Bullish
            strength = min(1.0, abs(macd_hist) * 100)
            description = "Bullish MACD Crossover"
        elif macd < macd_signal and macd_hist < 0:
            signal = -1  # Bearish
            strength = min(1.0, abs(macd_hist) * 100)
            description = "Bearish MACD Crossover"
        else:
            signal = 0   # Neutral
            strength = 0.5
            description = "MACD Neutral"

        return {
            'signal': signal,
            'strength': strength,
            'macd': macd,
            'signal_line': macd_signal,
            'histogram': macd_hist,
            'description': description
        }

    def _analyze_bollinger_bands(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Bollinger Bands Analyse"""
        if 'bb_position' not in df.columns:
            return {'signal': 0, 'strength': 0, 'description': 'Bollinger Bands nicht verfügbar'}

        bb_pos = df['bb_position'].iloc[-1]

        if bb_pos > 0.8:
            signal = -1  # Nahe oberer Band - Verkaufssignal
            strength = min(1.0, (bb_pos - 0.8) * 5)
            description = f"Nahe oberer Bollinger Band ({bb_pos:.2f})"
        elif bb_pos < 0.2:
            signal = 1   # Nahe unterer Band - Kaufsignal
            strength = min(1.0, (0.2 - bb_pos) * 5)
            description = f"Nahe unterer Bollinger Band ({bb_pos:.2f})"
        else:
            signal = 0   # Neutral
            strength = 0.5
            description = f"Bollinger Bands Neutral ({bb_pos:.2f})"

        return {
            'signal': signal,
            'strength': strength,
            'position': bb_pos,
            'description': description
        }

    def _analyze_moving_averages(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Moving Averages Analyse"""
        signals = []
        descriptions = []

        # SMA Crossovers
        if all(col in df.columns for col in ['sma_5', 'sma_20']):
            sma5 = df['sma_5'].iloc[-1]
            sma20 = df['sma_20'].iloc[-1]

            if sma5 > sma20:
                signals.append(1)
                descriptions.append("SMA5 > SMA20 (Bullish)")
            else:
                signals.append(-1)
                descriptions.append("SMA5 < SMA20 (Bearish)")

        # EMA Crossovers
        if all(col in df.columns for col in ['ema_5', 'ema_20']):
            ema5 = df['ema_5'].iloc[-1]
            ema20 = df['ema_20'].iloc[-1]

            if ema5 > ema20:
                signals.append(1)
                descriptions.append("EMA5 > EMA20 (Bullish)")
            else:
                signals.append(-1)
                descriptions.append("EMA5 < EMA20 (Bearish)")

        # Preis vs. Moving Averages
        if 'sma_20' in df.columns:
            price = df['Close'].iloc[-1]
            sma20 = df['sma_20'].iloc[-1]

            if price > sma20:
                signals.append(1)
                descriptions.append("Preis > SMA20")
            else:
                signals.append(-1)
                descriptions.append("Preis < SMA20")

        # Gesamtsignal
        if signals:
            avg_signal = np.mean(signals)
            strength = min(1.0, abs(avg_signal))
            description = "; ".join(descriptions)
        else:
            avg_signal = 0
            strength = 0
            description = "Moving Averages nicht verfügbar"

        return {
            'signal': avg_signal,
            'strength': strength,
            'individual_signals': signals,
            'description': description
        }

    def _analyze_volume(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Volume Analyse"""
        if 'Volume' not in df.columns:
            return {'signal': 0, 'strength': 0, 'description': 'Volume nicht verfügbar'}

        current_volume = df['Volume'].iloc[-1]
        avg_volume = df['Volume'].rolling(20).mean().iloc[-1]

        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

        # Preis-Volume Beziehung
        price_change = df['Close'].pct_change().iloc[-1]

        if volume_ratio > 1.5 and price_change > 0:
            signal = 1   # Hohe Volume + Preisanstieg = Bullish
            strength = min(1.0, volume_ratio / 3)
            description = f"Hohe Volume + Preisanstieg (Ratio: {volume_ratio:.1f})"
        elif volume_ratio > 1.5 and price_change < 0:
            signal = -1  # Hohe Volume + Preisrückgang = Bearish
            strength = min(1.0, volume_ratio / 3)
            description = f"Hohe Volume + Preisrückgang (Ratio: {volume_ratio:.1f})"
        else:
            signal = 0   # Normale Volume
            strength = 0.5
            description = f"Normale Volume (Ratio: {volume_ratio:.1f})"

        return {
            'signal': signal,
            'strength': strength,
            'volume_ratio': volume_ratio,
            'description': description
        }

    def _analyze_momentum(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Momentum Analyse"""
        signals = []
        descriptions = []

        # Price Momentum
        if len(df) >= 10:
            momentum_5 = df['Close'].iloc[-1] / df['Close'].iloc[-6] - 1
            momentum_10 = df['Close'].iloc[-1] / df['Close'].iloc[-11] - 1 if len(df) >= 11 else 0

            if momentum_5 > 0.02:  # 2% Anstieg in 5 Perioden
                signals.append(1)
                descriptions.append(f"5-Period Momentum: +{momentum_5*100:.1f}%")
            elif momentum_5 < -0.02:
                signals.append(-1)
                descriptions.append(f"5-Period Momentum: {momentum_5*100:.1f}%")

            if momentum_10 > 0.05:  # 5% Anstieg in 10 Perioden
                signals.append(1)
                descriptions.append(f"10-Period Momentum: +{momentum_10*100:.1f}%")
            elif momentum_10 < -0.05:
                signals.append(-1)
                descriptions.append(f"10-Period Momentum: {momentum_10*100:.1f}%")

        # Gesamtsignal
        if signals:
            avg_signal = np.mean(signals)
            strength = min(1.0, abs(avg_signal))
            description = "; ".join(descriptions)
        else:
            avg_signal = 0
            strength = 0
            description = "Momentum nicht berechenbar"

        return {
            'signal': avg_signal,
            'strength': strength,
            'description': description
        }

    def _analyze_volatility(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Volatilität Analyse"""
        if 'volatility_20' not in df.columns:
            return {'signal': 0, 'strength': 0, 'description': 'Volatilität nicht verfügbar'}

        current_vol = df['volatility_20'].iloc[-1]
        avg_vol = df['volatility_20'].rolling(50).mean().iloc[-1] if len(df) >= 50 else current_vol

        vol_ratio = current_vol / avg_vol if avg_vol > 0 else 1

        if vol_ratio > 1.5:
            signal = -0.5  # Hohe Volatilität = Vorsicht
            strength = min(1.0, vol_ratio / 2)
            description = f"Hohe Volatilität (Ratio: {vol_ratio:.1f})"
        elif vol_ratio < 0.7:
            signal = 0.5   # Niedrige Volatilität = Stabilität
            strength = min(1.0, (1 - vol_ratio))
            description = f"Niedrige Volatilität (Ratio: {vol_ratio:.1f})"
        else:
            signal = 0     # Normale Volatilität
            strength = 0.5
            description = f"Normale Volatilität (Ratio: {vol_ratio:.1f})"

        return {
            'signal': signal,
            'strength': strength,
            'volatility_ratio': vol_ratio,
            'description': description
        }

    def _analyze_support_resistance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Support/Resistance Analyse"""
        if len(df) < 20:
            return {'signal': 0, 'strength': 0, 'description': 'Nicht genügend Daten'}

        current_price = df['Close'].iloc[-1]

        # Lokale Maxima und Minima finden
        highs = df['High'].rolling(5, center=True).max()
        lows = df['Low'].rolling(5, center=True).min()

        # Resistance Levels (lokale Maxima)
        resistance_levels = []
        for i in range(5, len(df)-5):
            if df['High'].iloc[i] == highs.iloc[i]:
                resistance_levels.append(df['High'].iloc[i])

        # Support Levels (lokale Minima)
        support_levels = []
        for i in range(5, len(df)-5):
            if df['Low'].iloc[i] == lows.iloc[i]:
                support_levels.append(df['Low'].iloc[i])

        # Nächste Support/Resistance finden
        resistance_above = [r for r in resistance_levels if r > current_price]
        support_below = [s for s in support_levels if s < current_price]

        signal = 0
        strength = 0.5
        description = "Support/Resistance Neutral"

        if resistance_above:
            nearest_resistance = min(resistance_above)
            resistance_distance = (nearest_resistance - current_price) / current_price

            if resistance_distance < 0.02:  # Nahe Resistance
                signal = -0.5
                strength = 0.8
                description = f"Nahe Resistance bei {nearest_resistance:.0f}"

        if support_below:
            nearest_support = max(support_below)
            support_distance = (current_price - nearest_support) / current_price

            if support_distance < 0.02:  # Nahe Support
                signal = 0.5
                strength = 0.8
                description = f"Nahe Support bei {nearest_support:.0f}"

        return {
            'signal': signal,
            'strength': strength,
            'resistance_levels': resistance_levels[-5:],  # Letzte 5
            'support_levels': support_levels[-5:],        # Letzte 5
            'description': description
        }

    def _analyze_trend(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Trend Analyse"""
        if len(df) < 20:
            return {'signal': 0, 'strength': 0, 'description': 'Nicht genügend Daten'}

        # Verschiedene Zeiträume für Trend
        short_trend = df['Close'].iloc[-5:].pct_change().mean()  # 5 Perioden
        medium_trend = df['Close'].iloc[-10:].pct_change().mean()  # 10 Perioden
        long_trend = df['Close'].iloc[-20:].pct_change().mean()  # 20 Perioden

        trends = [short_trend, medium_trend, long_trend]
        trend_weights = [0.5, 0.3, 0.2]  # Kurzer Trend wichtiger

        weighted_trend = sum(t * w for t, w in zip(trends, trend_weights))

        if weighted_trend > 0.001:  # 0.1% pro Periode
            signal = 1
            strength = min(1.0, weighted_trend * 1000)
            description = f"Aufwärtstrend ({weighted_trend*100:.2f}%/Periode)"
        elif weighted_trend < -0.001:
            signal = -1
            strength = min(1.0, abs(weighted_trend) * 1000)
            description = f"Abwärtstrend ({weighted_trend*100:.2f}%/Periode)"
        else:
            signal = 0
            strength = 0.5
            description = "Seitwärtstrend"

        return {
            'signal': signal,
            'strength': strength,
            'short_trend': short_trend,
            'medium_trend': medium_trend,
            'long_trend': long_trend,
            'weighted_trend': weighted_trend,
            'description': description
        }

    def train(self, df: pd.DataFrame) -> bool:
        """Trainiere Technical Analysis (Gewichtungsoptimierung)"""
        try:
            LOGGERS['models'].info("📈 Starte Technical Analysis Training...")
            start_time = time.time()

            if len(df) < 50:
                LOGGERS['models'].warning("Nicht genügend Daten für Technical Analysis Training")
                return False

            # Historische Performance der Indikatoren bewerten
            performance_scores = {}

            for i in range(20, len(df) - 1):  # Sliding window
                historical_df = df.iloc[:i+1]
                indicators = self.calculate_all_indicators(historical_df)

                # Tatsächliche Preis-Änderung
                actual_change = df['Close'].iloc[i+1] / df['Close'].iloc[i] - 1
                actual_direction = 1 if actual_change > 0 else -1

                # Bewerte jeden Indikator
                for indicator_name, indicator_data in indicators.items():
                    if indicator_name not in performance_scores:
                        performance_scores[indicator_name] = []

                    predicted_signal = indicator_data.get('signal', 0)

                    # Accuracy Score: Stimmt die Richtung?
                    if predicted_signal * actual_direction > 0:
                        score = 1.0  # Richtige Richtung
                    elif predicted_signal == 0:
                        score = 0.5  # Neutral
                    else:
                        score = 0.0  # Falsche Richtung

                    # Gewichte mit Stärke des Signals
                    strength = indicator_data.get('strength', 0.5)
                    weighted_score = score * strength

                    performance_scores[indicator_name].append(weighted_score)

            # Optimiere Gewichtungen basierend auf Performance
            total_performance = 0
            for indicator_name, scores in performance_scores.items():
                if scores:
                    avg_performance = np.mean(scores)
                    self.performance_metrics[indicator_name] = {
                        'accuracy': avg_performance,
                        'samples': len(scores),
                        'std': np.std(scores)
                    }
                    total_performance += avg_performance

            # Normalisiere Gewichtungen
            if total_performance > 0:
                for indicator_name in self.weights.keys():
                    if indicator_name in self.performance_metrics:
                        performance = self.performance_metrics[indicator_name]['accuracy']
                        self.weights[indicator_name] = performance / total_performance
                    else:
                        self.weights[indicator_name] = 0.1  # Minimum weight

                # Normalisiere auf Summe = 1
                total_weight = sum(self.weights.values())
                if total_weight > 0:
                    for key in self.weights:
                        self.weights[key] /= total_weight

            self.is_trained = True

            training_time = time.time() - start_time
            LOGGERS['models'].info(f"✅ Technical Analysis Training abgeschlossen - {training_time:.1f}s")
            LOGGERS['models'].info(f"Optimierte Gewichtungen: {self.weights}")

            return True

        except Exception as e:
            LOGGERS['models'].error(f"❌ Technical Analysis Training Fehler: {e}")
            return False

    def predict(self, df: pd.DataFrame) -> Optional[PredictionResult]:
        """Erstelle Technical Analysis Prognose"""
        try:
            start_time = time.time()

            # Berechne alle Indikatoren
            indicators = self.calculate_all_indicators(df)

            if not indicators:
                return None

            # Gewichtete Gesamtbewertung
            total_signal = 0
            total_weight = 0
            signal_details = {}

            for indicator_name, indicator_data in indicators.items():
                signal = indicator_data.get('signal', 0)
                strength = indicator_data.get('strength', 0.5)
                weight = self.weights.get(indicator_name, 0.1)

                weighted_signal = signal * strength * weight
                total_signal += weighted_signal
                total_weight += weight

                signal_details[indicator_name] = {
                    'signal': signal,
                    'strength': strength,
                    'weight': weight,
                    'weighted_contribution': weighted_signal,
                    'description': indicator_data.get('description', '')
                }

            # Normalisiere Gesamtsignal
            if total_weight > 0:
                normalized_signal = total_signal / total_weight
            else:
                normalized_signal = 0

            # Aktuelle Preis-Informationen
            current_price = df['Close'].iloc[-1]

            # Prognose basierend auf Signal-Stärke
            signal_magnitude = abs(normalized_signal)

            # Konservative Preis-Prognose (Technical Analysis ist meist kurzfristig)
            if normalized_signal > 0.1:
                # Bullish Signal
                price_change_24h = signal_magnitude * 0.03  # Max 3% Änderung
                price_change_48h = signal_magnitude * 0.05  # Max 5% Änderung
                direction = "UP"
            elif normalized_signal < -0.1:
                # Bearish Signal
                price_change_24h = -signal_magnitude * 0.03
                price_change_48h = -signal_magnitude * 0.05
                direction = "DOWN"
            else:
                # Neutral
                price_change_24h = 0
                price_change_48h = 0
                direction = "SIDEWAYS"

            predicted_price_24h = current_price * (1 + price_change_24h)
            predicted_price_48h = current_price * (1 + price_change_48h)

            # Konfidenz basierend auf Signal-Konsistenz
            signal_consistency = len([s for s in signal_details.values()
                                    if s['signal'] * normalized_signal > 0]) / len(signal_details)

            confidence = min(0.95, max(0.1, signal_consistency * signal_magnitude))

            # Wahrscheinlichkeiten
            if direction == "UP":
                prob_up = confidence
                prob_down = 1 - confidence
            elif direction == "DOWN":
                prob_up = 1 - confidence
                prob_down = confidence
            else:
                prob_up = 0.5
                prob_down = 0.5

            # Risk Level basierend auf Volatilität und Signal-Stärke
            volatility = df['Close'].rolling(20).std().iloc[-1] / current_price if len(df) >= 20 else 0.02

            if volatility < 0.02 and signal_magnitude < 0.3:
                risk_level = "LOW"
            elif volatility < 0.05 and signal_magnitude < 0.6:
                risk_level = "MEDIUM"
            else:
                risk_level = "HIGH"

            execution_time = (time.time() - start_time) * 1000

            # Erstelle detaillierte Feature-Liste
            features_used = []
            for indicator_name, details in signal_details.items():
                if details['weighted_contribution'] != 0:
                    features_used.append(f"{indicator_name} ({details['signal']:.2f})")

            return PredictionResult(
                model_name="Technical Analysis Master",
                timestamp=datetime.now(),
                current_price=current_price,
                predicted_price_24h=predicted_price_24h,
                predicted_price_48h=predicted_price_48h,
                confidence=confidence,
                direction=direction,
                probability_up=prob_up,
                probability_down=prob_down,
                risk_level=risk_level,
                features_used=features_used,
                execution_time_ms=execution_time,
                metadata={
                    'normalized_signal': normalized_signal,
                    'signal_magnitude': signal_magnitude,
                    'signal_consistency': signal_consistency,
                    'indicator_details': signal_details,
                    'weights_used': self.weights,
                    'volatility': volatility
                }
            )

        except Exception as e:
            LOGGERS['models'].error(f"❌ Technical Analysis Prediction Fehler: {e}")
            return None

# ============================================================================
# HAUPTSYSTEM - ULTIMATE MASTER TRADING SYSTEM
# ============================================================================

class UltimateMasterTradingSystem:
    """🚀 Hauptsystem - Koordiniert alle Komponenten"""

    def __init__(self):
        self.config = SystemConfig()
        self.status = SystemStatus.INITIALIZING

        # Core Komponenten
        self.resource_manager = GlobalResourceManager()
        self.system_monitor = SystemMonitor()
        self.data_provider = RealTimeDataProvider()
        self.feature_engineer = FeatureEngineer()

        # Prognosemodelle
        self.models = {
            'lstm': LSTMDeepLearningModel(),
            'ensemble': ChampionEnsembleModel(),
            'technical': TechnicalAnalysisMaster()
        }

        # Threading
        self.stop_event = threading.Event()
        self.model_threads = {}
        self.data_thread = None

        # Ergebnisse
        self.latest_predictions = {}
        self.prediction_history = []
        self.system_stats = {}

        # GUI
        self.dashboard = None

        # Registriere bei Resource Manager
        self.resource_manager.register_system(self)

        LOGGERS['system'].info("🚀 Ultimate Master Trading System initialisiert")

    def initialize(self) -> bool:
        """Initialisiere das System"""
        try:
            LOGGERS['system'].info("🔧 Initialisiere System...")

            # System-Monitoring starten
            self.system_monitor.start_monitoring()

            # Initiale Daten laden
            initial_data = self.data_provider.get_historical_data(hours=48)
            if initial_data.empty:
                LOGGERS['system'].error("❌ Keine initialen Daten verfügbar")
                return False

            # Features erstellen
            enhanced_data = self.feature_engineer.engineer_features(initial_data)

            # Modelle initialisieren
            for model_name, model in self.models.items():
                try:
                    LOGGERS['system'].info(f"Initialisiere {model_name} Modell...")
                    # Hier könnten gespeicherte Modelle geladen werden
                    LOGGERS['system'].info(f"✅ {model_name} Modell bereit")
                except Exception as e:
                    LOGGERS['system'].error(f"❌ {model_name} Initialisierung fehlgeschlagen: {e}")

            self.status = SystemStatus.RUNNING
            LOGGERS['system'].info("✅ System erfolgreich initialisiert")
            return True

        except Exception as e:
            LOGGERS['system'].error(f"❌ System-Initialisierung fehlgeschlagen: {e}")
            self.status = SystemStatus.ERROR
            return False

    def start_continuous_operation(self):
        """Starte kontinuierlichen Betrieb"""
        try:
            LOGGERS['system'].info("🚀 Starte kontinuierlichen Betrieb...")

            # Daten-Update Thread
            self.data_thread = threading.Thread(
                target=self._continuous_data_update,
                daemon=True
            )
            self.data_thread.start()

            # Model Training Threads
            for model_name in self.models.keys():
                thread = threading.Thread(
                    target=self._continuous_model_operation,
                    args=(model_name,),
                    daemon=True
                )
                self.model_threads[model_name] = thread
                thread.start()

            LOGGERS['system'].info("✅ Kontinuierlicher Betrieb gestartet")

        except Exception as e:
            LOGGERS['system'].error(f"❌ Fehler beim Starten des kontinuierlichen Betriebs: {e}")

    def _continuous_data_update(self):
        """Kontinuierliche Daten-Updates"""
        while not self.stop_event.is_set():
            try:
                # Aktuelle Daten holen
                self.data_provider.update_price_history()

                # System-Stats aktualisieren
                self._update_system_stats()

                # Warten bis zum nächsten Update
                self.stop_event.wait(self.config.data_update_interval)

            except Exception as e:
                LOGGERS['data'].error(f"Daten-Update Fehler: {e}")
                self.stop_event.wait(60)  # 1 Minute warten bei Fehler

    def _continuous_model_operation(self, model_name: str):
        """Kontinuierlicher Modell-Betrieb"""
        model = self.models[model_name]
        training_interval = self.config.training_intervals.get(model_name, 300)

        while not self.stop_event.is_set():
            try:
                # Aktuelle Daten holen
                data = self.data_provider.get_historical_data(hours=48)
                if not data.empty:
                    # Features erstellen
                    enhanced_data = self.feature_engineer.engineer_features(data)

                    # Training (falls nötig)
                    if not model.is_trained or self._should_retrain(model_name):
                        LOGGERS['models'].info(f"🔄 Trainiere {model_name} Modell...")
                        success = model.train(enhanced_data)
                        if success:
                            LOGGERS['models'].info(f"✅ {model_name} Training erfolgreich")
                        else:
                            LOGGERS['models'].warning(f"⚠️ {model_name} Training fehlgeschlagen")

                    # Vorhersage erstellen
                    prediction = model.predict(enhanced_data)
                    if prediction:
                        self.latest_predictions[model_name] = prediction
                        self._add_to_prediction_history(prediction)
                        LOGGERS['models'].info(f"📊 {model_name} Vorhersage: {prediction.direction} "
                                             f"(Konfidenz: {prediction.confidence:.2f})")

                # Warten bis zum nächsten Zyklus
                self.stop_event.wait(training_interval)

            except Exception as e:
                LOGGERS['models'].error(f"{model_name} Betrieb Fehler: {e}")
                self.stop_event.wait(60)

    def _should_retrain(self, model_name: str) -> bool:
        """Bestimme ob Modell neu trainiert werden sollte"""
        # Einfache Logik: Retrain alle X Minuten
        training_intervals = {
            'lstm': 30,      # 30 Minuten
            'ensemble': 20,  # 20 Minuten
            'technical': 10  # 10 Minuten
        }

        interval = training_intervals.get(model_name, 30)

        # Hier könnte komplexere Logik implementiert werden
        # z.B. basierend auf Performance-Verschlechterung
        return True  # Für jetzt immer retrain

    def _add_to_prediction_history(self, prediction: PredictionResult):
        """Füge Vorhersage zur Historie hinzu"""
        self.prediction_history.append(prediction)

        # Historie begrenzen
        max_history = 1000
        if len(self.prediction_history) > max_history:
            self.prediction_history = self.prediction_history[-max_history:]

    def _update_system_stats(self):
        """Aktualisiere System-Statistiken"""
        stats = self.system_monitor.get_latest_stats()
        if stats:
            self.system_stats = {
                'timestamp': datetime.now(),
                'system_status': self.status.value,
                'active_models': len([m for m in self.models.values() if m.is_trained]),
                'total_predictions': len(self.prediction_history),
                'current_price': self.data_provider.current_price,
                'system_stats': stats
            }

    def get_ensemble_prediction(self) -> Optional[PredictionResult]:
        """Erstelle Ensemble-Vorhersage aus allen Modellen"""
        if not self.latest_predictions:
            return None

        try:
            # Gewichtungen für Ensemble
            model_weights = {
                'lstm': 0.4,
                'ensemble': 0.35,
                'technical': 0.25
            }

            # Sammle Vorhersagen
            predictions = []
            weights = []

            for model_name, prediction in self.latest_predictions.items():
                if prediction and model_name in model_weights:
                    predictions.append(prediction)
                    weights.append(model_weights[model_name])

            if not predictions:
                return None

            # Normalisiere Gewichtungen
            total_weight = sum(weights)
            weights = [w / total_weight for w in weights]

            # Gewichtete Durchschnitte
            current_price = predictions[0].current_price

            weighted_price_24h = sum(p.predicted_price_24h * w for p, w in zip(predictions, weights))
            weighted_price_48h = sum(p.predicted_price_48h * w for p, w in zip(predictions, weights))
            weighted_confidence = sum(p.confidence * w for p, w in zip(predictions, weights))
            weighted_prob_up = sum(p.probability_up * w for p, w in zip(predictions, weights))
            weighted_prob_down = sum(p.probability_down * w for p, w in zip(predictions, weights))

            # Ensemble-Richtung
            if weighted_price_24h > current_price:
                direction = "UP"
            elif weighted_price_24h < current_price:
                direction = "DOWN"
            else:
                direction = "SIDEWAYS"

            # Risk Level (konservativ)
            risk_levels = [p.risk_level for p in predictions]
            if "HIGH" in risk_levels:
                risk_level = "HIGH"
            elif "MEDIUM" in risk_levels:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"

            # Features kombinieren
            all_features = []
            for p in predictions:
                all_features.extend(p.features_used)

            return PredictionResult(
                model_name="Master Ensemble",
                timestamp=datetime.now(),
                current_price=current_price,
                predicted_price_24h=weighted_price_24h,
                predicted_price_48h=weighted_price_48h,
                confidence=weighted_confidence,
                direction=direction,
                probability_up=weighted_prob_up,
                probability_down=weighted_prob_down,
                risk_level=risk_level,
                features_used=list(set(all_features)),
                execution_time_ms=0,
                metadata={
                    'ensemble_weights': dict(zip([p.model_name for p in predictions], weights)),
                    'individual_predictions': {p.model_name: {
                        'price_24h': p.predicted_price_24h,
                        'confidence': p.confidence,
                        'direction': p.direction
                    } for p in predictions}
                }
            )

        except Exception as e:
            LOGGERS['system'].error(f"Ensemble prediction error: {e}")
            return None

    def stop(self):
        """Stoppe das System"""
        LOGGERS['system'].info("🛑 Stoppe System...")

        self.status = SystemStatus.STOPPING
        self.stop_event.set()

        # Warte auf Threads
        if self.data_thread:
            self.data_thread.join(timeout=5)

        for thread in self.model_threads.values():
            thread.join(timeout=5)

        # System-Monitoring stoppen
        self.system_monitor.stop_monitoring()

        self.status = SystemStatus.STOPPED
        LOGGERS['system'].info("✅ System gestoppt")

    def force_cleanup(self):
        """Force Cleanup für Emergency Shutdown"""
        try:
            self.stop()
            if self.dashboard:
                self.dashboard.destroy()
        except Exception as e:
            LOGGERS['system'].error(f"Force cleanup error: {e}")

    def create_dashboard(self):
        """Erstelle und starte Dashboard"""
        try:
            self.dashboard = UltimateMasterDashboard(self)
            return self.dashboard
        except Exception as e:
            LOGGERS['gui'].error(f"Dashboard creation error: {e}")
            return None

# ============================================================================
# ULTIMATE MASTER DASHBOARD - MODERNES GUI
# ============================================================================

class UltimateMasterDashboard:
    """🎨 Modernes Dashboard mit Live-Monitoring"""

    def __init__(self, trading_system: UltimateMasterTradingSystem):
        self.trading_system = trading_system
        self.root = None
        self.notebook = None

        # GUI Komponenten
        self.charts = {}
        self.status_labels = {}
        self.progress_bars = {}
        self.log_widgets = {}

        # Chart-Daten
        self.chart_data = {}
        self.crosshair_lines = {}
        self.zoom_level = 1.0
        self.time_range = 24  # Stunden

        # Update-Threading
        self.gui_update_thread = None
        self.gui_stop_event = threading.Event()

        # Farben (Dark Theme)
        self.colors = {
            'bg_primary': '#1e1e1e',
            'bg_secondary': '#2d2d2d',
            'bg_tertiary': '#3d3d3d',
            'text_primary': '#ffffff',
            'text_secondary': '#cccccc',
            'accent_bitcoin': '#f7931a',
            'accent_success': '#00d4aa',
            'accent_error': '#ff4757',
            'accent_warning': '#ffa502',
            'accent_info': '#3742fa'
        }

        self._create_gui()
        self._start_gui_updates()

        LOGGERS['gui'].info("🎨 Ultimate Master Dashboard erstellt")

    def _create_gui(self):
        """Erstelle GUI"""
        # Hauptfenster
        self.root = tk.Tk()
        self.root.title("🚀 Ultimate Master Bitcoin Trading System")
        self.root.geometry("1920x1080")
        self.root.configure(bg=self.colors['bg_primary'])
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # Style konfigurieren
        style = ttk.Style()
        style.theme_use('clam')

        # Dark Theme Style
        style.configure('TNotebook', background=self.colors['bg_primary'])
        style.configure('TNotebook.Tab', background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'])
        style.configure('TFrame', background=self.colors['bg_primary'])
        style.configure('TLabel', background=self.colors['bg_primary'],
                       foreground=self.colors['text_primary'])
        style.configure('TButton', background=self.colors['bg_secondary'])

        # Hauptlayout
        self._create_main_layout()
        self._create_tabs()

    def _create_main_layout(self):
        """Erstelle Hauptlayout"""
        # Header
        header_frame = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=80)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)

        # Titel
        title_label = tk.Label(
            header_frame,
            text="🚀 ULTIMATE MASTER BITCOIN TRADING SYSTEM",
            font=('Arial', 16, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['accent_bitcoin']
        )
        title_label.pack(side='left', padx=20, pady=20)

        # System Status
        self.system_status_label = tk.Label(
            header_frame,
            text="System: Initialisierung...",
            font=('Arial', 12),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_secondary']
        )
        self.system_status_label.pack(side='right', padx=20, pady=20)

        # Notebook für Tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)

    def _create_tabs(self):
        """Erstelle alle Tabs"""
        # 1. Übersicht Tab
        self._create_overview_tab()

        # 2. LSTM Modell Tab
        self._create_model_tab('LSTM Deep Learning', 'lstm')

        # 3. Ensemble Modell Tab
        self._create_model_tab('Champion Ensemble', 'ensemble')

        # 4. Technical Analysis Tab
        self._create_model_tab('Technical Analysis', 'technical')

        # 5. System Monitoring Tab
        self._create_monitoring_tab()

        # 6. Logs Tab
        self._create_logs_tab()

    def _create_overview_tab(self):
        """Erstelle Übersicht Tab"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="📊 Übersicht")

        # Linke Seite: Live Chart
        left_frame = tk.Frame(overview_frame, bg=self.colors['bg_primary'])
        left_frame.pack(side='left', fill='both', expand=True, padx=5, pady=5)

        # Chart Titel
        chart_title = tk.Label(
            left_frame,
            text="📈 Bitcoin Preis & Ensemble Prognose",
            font=('Arial', 14, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        )
        chart_title.pack(pady=10)

        # Chart Controls
        controls_frame = tk.Frame(left_frame, bg=self.colors['bg_primary'])
        controls_frame.pack(fill='x', padx=10, pady=5)

        # Zeitbereich Buttons
        time_ranges = [('1h', 1), ('6h', 6), ('24h', 24), ('48h', 48), ('1w', 168)]
        for label, hours in time_ranges:
            btn = tk.Button(
                controls_frame,
                text=label,
                command=lambda h=hours: self._change_time_range(h),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary'],
                relief='flat',
                padx=10
            )
            btn.pack(side='left', padx=2)

        # Zoom Controls
        zoom_frame = tk.Frame(controls_frame, bg=self.colors['bg_primary'])
        zoom_frame.pack(side='right')

        tk.Button(
            zoom_frame,
            text="🔍+",
            command=lambda: self._zoom_chart(1.2),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            relief='flat'
        ).pack(side='left', padx=2)

        tk.Button(
            zoom_frame,
            text="🔍-",
            command=lambda: self._zoom_chart(0.8),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            relief='flat'
        ).pack(side='left', padx=2)

        # Matplotlib Chart
        self.overview_fig = Figure(figsize=(12, 8), facecolor=self.colors['bg_primary'])
        self.overview_ax = self.overview_fig.add_subplot(111, facecolor=self.colors['bg_secondary'])

        self.overview_canvas = FigureCanvasTkAgg(self.overview_fig, left_frame)
        self.overview_canvas.get_tk_widget().pack(fill='both', expand=True, padx=10, pady=10)

        # Crosshair Setup
        self.overview_canvas.mpl_connect('motion_notify_event', self._on_mouse_move)

        # Rechte Seite: Prognose-Karten
        right_frame = tk.Frame(overview_frame, bg=self.colors['bg_primary'], width=400)
        right_frame.pack(side='right', fill='y', padx=5, pady=5)
        right_frame.pack_propagate(False)

        # Prognose Titel
        pred_title = tk.Label(
            right_frame,
            text="🎯 Live Prognosen",
            font=('Arial', 14, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        )
        pred_title.pack(pady=10)

        # Ensemble Prognose Karte
        self._create_prediction_card(right_frame, "Master Ensemble", "ensemble_card")

        # Einzelmodell Karten
        for model_name, display_name in [
            ('lstm', 'LSTM Deep Learning'),
            ('ensemble', 'Champion Ensemble'),
            ('technical', 'Technical Analysis')
        ]:
            self._create_prediction_card(right_frame, display_name, f"{model_name}_card")

    def _create_prediction_card(self, parent, title, card_id):
        """Erstelle Prognose-Karte"""
        card_frame = tk.Frame(
            parent,
            bg=self.colors['bg_secondary'],
            relief='raised',
            bd=1
        )
        card_frame.pack(fill='x', padx=10, pady=5)

        # Titel
        title_label = tk.Label(
            card_frame,
            text=title,
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        title_label.pack(pady=5)

        # Prognose Daten
        data_frame = tk.Frame(card_frame, bg=self.colors['bg_secondary'])
        data_frame.pack(fill='x', padx=10, pady=5)

        # Labels für Daten
        labels = {}
        for key, text in [
            ('direction', 'Richtung:'),
            ('price_24h', '24h Preis:'),
            ('confidence', 'Konfidenz:'),
            ('risk', 'Risiko:')
        ]:
            row_frame = tk.Frame(data_frame, bg=self.colors['bg_secondary'])
            row_frame.pack(fill='x', pady=2)

            tk.Label(
                row_frame,
                text=text,
                font=('Arial', 10),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary']
            ).pack(side='left')

            value_label = tk.Label(
                row_frame,
                text="--",
                font=('Arial', 10, 'bold'),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_primary']
            )
            value_label.pack(side='right')

            labels[key] = value_label

        # Speichere Labels für Updates
        self.status_labels[card_id] = labels

    def _create_model_tab(self, title, model_key):
        """Erstelle Modell-spezifischen Tab"""
        model_frame = ttk.Frame(self.notebook)
        self.notebook.add(model_frame, text=title)

        # Oberer Bereich: Controls
        control_frame = tk.Frame(model_frame, bg=self.colors['bg_secondary'], height=100)
        control_frame.pack(fill='x', padx=5, pady=5)
        control_frame.pack_propagate(False)

        # Start/Stop Buttons
        btn_frame = tk.Frame(control_frame, bg=self.colors['bg_secondary'])
        btn_frame.pack(side='left', padx=20, pady=20)

        start_btn = tk.Button(
            btn_frame,
            text="▶️ Start Training",
            command=lambda: self._start_model_training(model_key),
            bg=self.colors['accent_success'],
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=15
        )
        start_btn.pack(side='left', padx=5)

        stop_btn = tk.Button(
            btn_frame,
            text="⏹️ Stop",
            command=lambda: self._stop_model_training(model_key),
            bg=self.colors['accent_error'],
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=15
        )
        stop_btn.pack(side='left', padx=5)

        # Status und Progress
        status_frame = tk.Frame(control_frame, bg=self.colors['bg_secondary'])
        status_frame.pack(side='right', padx=20, pady=20)

        status_label = tk.Label(
            status_frame,
            text=f"{title}: Bereit",
            font=('Arial', 12),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        status_label.pack()

        progress = ttk.Progressbar(
            status_frame,
            mode='indeterminate',
            length=200
        )
        progress.pack(pady=5)

        self.status_labels[f"{model_key}_status"] = status_label
        self.progress_bars[model_key] = progress

        # Mittlerer Bereich: Chart
        chart_frame = tk.Frame(model_frame, bg=self.colors['bg_primary'])
        chart_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # Model-spezifisches Chart
        fig = Figure(figsize=(14, 6), facecolor=self.colors['bg_primary'])
        ax = fig.add_subplot(111, facecolor=self.colors['bg_secondary'])

        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.get_tk_widget().pack(fill='both', expand=True)

        self.charts[model_key] = {'fig': fig, 'ax': ax, 'canvas': canvas}

        # Unterer Bereich: Logs
        log_frame = tk.Frame(model_frame, bg=self.colors['bg_primary'], height=150)
        log_frame.pack(fill='x', padx=5, pady=5)
        log_frame.pack_propagate(False)

        log_title = tk.Label(
            log_frame,
            text=f"📋 {title} Logs",
            font=('Arial', 12, 'bold'),
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        )
        log_title.pack(anchor='w', padx=10, pady=5)

        log_text = scrolledtext.ScrolledText(
            log_frame,
            height=6,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            font=('Consolas', 9)
        )
        log_text.pack(fill='both', expand=True, padx=10, pady=5)

        self.log_widgets[model_key] = log_text

    def _create_monitoring_tab(self):
        """Erstelle System-Monitoring Tab"""
        monitor_frame = ttk.Frame(self.notebook)
        self.notebook.add(monitor_frame, text="🔍 System Monitor")

        # System Stats
        stats_frame = tk.Frame(monitor_frame, bg=self.colors['bg_secondary'], height=200)
        stats_frame.pack(fill='x', padx=5, pady=5)
        stats_frame.pack_propagate(False)

        stats_title = tk.Label(
            stats_frame,
            text="💻 System Statistiken",
            font=('Arial', 14, 'bold'),
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        stats_title.pack(pady=10)

        # Stats Grid
        stats_grid = tk.Frame(stats_frame, bg=self.colors['bg_secondary'])
        stats_grid.pack(fill='both', expand=True, padx=20, pady=10)

        # CPU, RAM, GPU Stats
        self.system_stats_labels = {}
        stats_items = [
            ('cpu', 'CPU Auslastung:', '0%'),
            ('memory', 'RAM Verbrauch:', '0%'),
            ('gpu', 'GPU Auslastung:', 'N/A'),
            ('threads', 'Aktive Threads:', '0'),
            ('predictions', 'Prognosen Total:', '0'),
            ('uptime', 'Laufzeit:', '00:00:00')
        ]

        for i, (key, label, default) in enumerate(stats_items):
            row = i // 2
            col = i % 2

            item_frame = tk.Frame(stats_grid, bg=self.colors['bg_secondary'])
            item_frame.grid(row=row, column=col, sticky='ew', padx=10, pady=5)

            tk.Label(
                item_frame,
                text=label,
                font=('Arial', 11),
                bg=self.colors['bg_secondary'],
                fg=self.colors['text_secondary']
            ).pack(side='left')

            value_label = tk.Label(
                item_frame,
                text=default,
                font=('Arial', 11, 'bold'),
                bg=self.colors['bg_secondary'],
                fg=self.colors['accent_info']
            )
            value_label.pack(side='right')

            self.system_stats_labels[key] = value_label

        stats_grid.columnconfigure(0, weight=1)
        stats_grid.columnconfigure(1, weight=1)

        # Performance Charts
        perf_frame = tk.Frame(monitor_frame, bg=self.colors['bg_primary'])
        perf_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # CPU/RAM Chart
        self.monitor_fig = Figure(figsize=(14, 8), facecolor=self.colors['bg_primary'])

        # CPU Chart
        self.cpu_ax = self.monitor_fig.add_subplot(221, facecolor=self.colors['bg_secondary'])
        self.cpu_ax.set_title('CPU Auslastung (%)', color=self.colors['text_primary'])
        self.cpu_ax.set_ylim(0, 100)

        # RAM Chart
        self.ram_ax = self.monitor_fig.add_subplot(222, facecolor=self.colors['bg_secondary'])
        self.ram_ax.set_title('RAM Verbrauch (%)', color=self.colors['text_primary'])
        self.ram_ax.set_ylim(0, 100)

        # Threads Chart
        self.threads_ax = self.monitor_fig.add_subplot(223, facecolor=self.colors['bg_secondary'])
        self.threads_ax.set_title('Aktive Threads', color=self.colors['text_primary'])

        # Predictions Chart
        self.pred_ax = self.monitor_fig.add_subplot(224, facecolor=self.colors['bg_secondary'])
        self.pred_ax.set_title('Prognosen pro Minute', color=self.colors['text_primary'])

        self.monitor_canvas = FigureCanvasTkAgg(self.monitor_fig, perf_frame)
        self.monitor_canvas.get_tk_widget().pack(fill='both', expand=True)

        # Style Charts
        for ax in [self.cpu_ax, self.ram_ax, self.threads_ax, self.pred_ax]:
            ax.tick_params(colors=self.colors['text_secondary'])
            ax.spines['bottom'].set_color(self.colors['text_secondary'])
            ax.spines['top'].set_color(self.colors['text_secondary'])
            ax.spines['right'].set_color(self.colors['text_secondary'])
            ax.spines['left'].set_color(self.colors['text_secondary'])

    def _create_logs_tab(self):
        """Erstelle Logs Tab"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📋 System Logs")

        # Log Controls
        control_frame = tk.Frame(logs_frame, bg=self.colors['bg_secondary'], height=60)
        control_frame.pack(fill='x', padx=5, pady=5)
        control_frame.pack_propagate(False)

        # Clear Button
        clear_btn = tk.Button(
            control_frame,
            text="🗑️ Clear Logs",
            command=self._clear_logs,
            bg=self.colors['accent_warning'],
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=15
        )
        clear_btn.pack(side='left', padx=20, pady=15)

        # Auto-scroll Checkbox
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_cb = tk.Checkbutton(
            control_frame,
            text="Auto-Scroll",
            variable=self.auto_scroll_var,
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary'],
            selectcolor=self.colors['bg_tertiary']
        )
        auto_scroll_cb.pack(side='left', padx=20, pady=15)

        # Main Log Display
        self.main_log_text = scrolledtext.ScrolledText(
            logs_frame,
            bg=self.colors['bg_tertiary'],
            fg=self.colors['text_primary'],
            font=('Consolas', 9),
            wrap=tk.WORD
        )
        self.main_log_text.pack(fill='both', expand=True, padx=10, pady=10)

    def _start_gui_updates(self):
        """Starte GUI Update Thread"""
        self.gui_update_thread = threading.Thread(
            target=self._gui_update_loop,
            daemon=True
        )
        self.gui_update_thread.start()

    def _gui_update_loop(self):
        """GUI Update Loop"""
        while not self.gui_stop_event.is_set():
            try:
                # Update alle GUI Komponenten
                self.root.after(0, self._update_overview_chart)
                self.root.after(0, self._update_prediction_cards)
                self.root.after(0, self._update_system_stats)
                self.root.after(0, self._update_model_charts)
                self.root.after(0, self._update_monitoring_charts)

                # Warte 1 Sekunde
                self.gui_stop_event.wait(1)

            except Exception as e:
                LOGGERS['gui'].error(f"GUI update error: {e}")
                self.gui_stop_event.wait(5)

    def _update_overview_chart(self):
        """Update Übersicht Chart"""
        try:
            # Hole aktuelle Daten
            data = self.trading_system.data_provider.get_historical_data(hours=self.time_range)
            if data.empty:
                return

            # Clear und neu zeichnen
            self.overview_ax.clear()

            # Bitcoin Preis
            self.overview_ax.plot(
                data.index, data['Close'],
                color=self.colors['accent_bitcoin'],
                linewidth=2,
                label='Bitcoin Preis'
            )

            # Ensemble Prognose (falls verfügbar)
            ensemble_pred = self.trading_system.get_ensemble_prediction()
            if ensemble_pred:
                current_time = datetime.now()
                future_24h = current_time + timedelta(hours=24)
                future_48h = current_time + timedelta(hours=48)

                # Prognose Linien
                self.overview_ax.plot(
                    [current_time, future_24h],
                    [ensemble_pred.current_price, ensemble_pred.predicted_price_24h],
                    color=self.colors['accent_success'],
                    linestyle='--',
                    linewidth=2,
                    label='24h Prognose'
                )

                self.overview_ax.plot(
                    [current_time, future_48h],
                    [ensemble_pred.current_price, ensemble_pred.predicted_price_48h],
                    color=self.colors['accent_info'],
                    linestyle='--',
                    linewidth=2,
                    label='48h Prognose'
                )

            # Chart Styling
            self.overview_ax.set_title(
                f'Bitcoin Preis & Prognosen ({self.time_range}h)',
                color=self.colors['text_primary'],
                fontsize=14
            )
            self.overview_ax.set_ylabel('Preis (USD)', color=self.colors['text_primary'])
            self.overview_ax.legend(loc='upper left')
            self.overview_ax.grid(True, alpha=0.3)

            # Farben anpassen
            self.overview_ax.tick_params(colors=self.colors['text_secondary'])
            for spine in self.overview_ax.spines.values():
                spine.set_color(self.colors['text_secondary'])

            # Canvas aktualisieren
            self.overview_canvas.draw()

        except Exception as e:
            LOGGERS['gui'].error(f"Overview chart update error: {e}")

    def _update_prediction_cards(self):
        """Update Prognose-Karten"""
        try:
            # Ensemble Prognose
            ensemble_pred = self.trading_system.get_ensemble_prediction()
            if ensemble_pred and 'ensemble_card' in self.status_labels:
                self._update_single_card('ensemble_card', ensemble_pred)

            # Einzelmodell Prognosen
            for model_key in ['lstm', 'ensemble', 'technical']:
                if model_key in self.trading_system.latest_predictions:
                    pred = self.trading_system.latest_predictions[model_key]
                    card_key = f"{model_key}_card"
                    if card_key in self.status_labels:
                        self._update_single_card(card_key, pred)

        except Exception as e:
            LOGGERS['gui'].error(f"Prediction cards update error: {e}")

    def _update_single_card(self, card_key, prediction):
        """Update einzelne Prognose-Karte"""
        try:
            labels = self.status_labels[card_key]

            # Richtung mit Farbe
            direction_color = self.colors['accent_success'] if prediction.direction == "UP" else \
                             self.colors['accent_error'] if prediction.direction == "DOWN" else \
                             self.colors['text_secondary']

            labels['direction'].config(text=prediction.direction, fg=direction_color)
            labels['price_24h'].config(text=f"${prediction.predicted_price_24h:,.0f}")
            labels['confidence'].config(text=f"{prediction.confidence*100:.1f}%")

            # Risk Level mit Farbe
            risk_color = self.colors['accent_success'] if prediction.risk_level == "LOW" else \
                        self.colors['accent_warning'] if prediction.risk_level == "MEDIUM" else \
                        self.colors['accent_error']

            labels['risk'].config(text=prediction.risk_level, fg=risk_color)

        except Exception as e:
            LOGGERS['gui'].error(f"Single card update error: {e}")

    def _update_system_stats(self):
        """Update System-Statistiken"""
        try:
            stats = self.trading_system.system_monitor.get_latest_stats()
            if not stats:
                return

            # Update Labels
            self.system_stats_labels['cpu'].config(text=f"{stats['cpu_percent']:.1f}%")
            self.system_stats_labels['memory'].config(text=f"{stats['memory_percent']:.1f}%")
            self.system_stats_labels['threads'].config(text=str(stats['active_threads']))

            # GPU (falls verfügbar)
            if stats['gpu_stats']:
                gpu_text = f"{stats['gpu_stats']['gpu_percent']:.1f}%"
            else:
                gpu_text = "N/A"
            self.system_stats_labels['gpu'].config(text=gpu_text)

            # Prognosen Total
            total_predictions = len(self.trading_system.prediction_history)
            self.system_stats_labels['predictions'].config(text=str(total_predictions))

            # System Status
            status_text = f"System: {self.trading_system.status.value.title()}"
            self.system_status_label.config(text=status_text)

        except Exception as e:
            LOGGERS['gui'].error(f"System stats update error: {e}")

    def _update_model_charts(self):
        """Update Modell-spezifische Charts"""
        # Hier könnten modell-spezifische Charts implementiert werden
        pass

    def _update_monitoring_charts(self):
        """Update Monitoring Charts"""
        try:
            stats_history = self.trading_system.system_monitor.get_stats_history()
            if len(stats_history) < 2:
                return

            # Letzte 50 Datenpunkte
            recent_stats = stats_history[-50:]
            timestamps = [s['timestamp'] for s in recent_stats]

            # CPU Chart
            cpu_values = [s['cpu_percent'] for s in recent_stats]
            self.cpu_ax.clear()
            self.cpu_ax.plot(timestamps, cpu_values, color=self.colors['accent_info'], linewidth=2)
            self.cpu_ax.set_ylim(0, 100)
            self.cpu_ax.set_title('CPU Auslastung (%)', color=self.colors['text_primary'])

            # RAM Chart
            ram_values = [s['memory_percent'] for s in recent_stats]
            self.ram_ax.clear()
            self.ram_ax.plot(timestamps, ram_values, color=self.colors['accent_warning'], linewidth=2)
            self.ram_ax.set_ylim(0, 100)
            self.ram_ax.set_title('RAM Verbrauch (%)', color=self.colors['text_primary'])

            # Threads Chart
            thread_values = [s['active_threads'] for s in recent_stats]
            self.threads_ax.clear()
            self.threads_ax.plot(timestamps, thread_values, color=self.colors['accent_success'], linewidth=2)
            self.threads_ax.set_title('Aktive Threads', color=self.colors['text_primary'])

            # Style alle Charts
            for ax in [self.cpu_ax, self.ram_ax, self.threads_ax]:
                ax.tick_params(colors=self.colors['text_secondary'])
                for spine in ax.spines.values():
                    spine.set_color(self.colors['text_secondary'])
                ax.grid(True, alpha=0.3)

            self.monitor_canvas.draw()

        except Exception as e:
            LOGGERS['gui'].error(f"Monitoring charts update error: {e}")

    # Event Handlers
    def _change_time_range(self, hours):
        """Ändere Zeitbereich"""
        self.time_range = hours
        LOGGERS['gui'].info(f"Zeitbereich geändert auf {hours}h")

    def _zoom_chart(self, factor):
        """Zoom Chart"""
        self.zoom_level *= factor
        LOGGERS['gui'].info(f"Zoom Level: {self.zoom_level:.1f}")

    def _on_mouse_move(self, event):
        """Mouse Move Event für Crosshair"""
        if event.inaxes == self.overview_ax:
            # Hier könnte Crosshair-Funktionalität implementiert werden
            pass

    def _start_model_training(self, model_key):
        """Starte Modell Training"""
        LOGGERS['gui'].info(f"Starte {model_key} Training")
        if model_key in self.progress_bars:
            self.progress_bars[model_key].start()
        if f"{model_key}_status" in self.status_labels:
            self.status_labels[f"{model_key}_status"].config(text=f"{model_key.title()}: Training...")

    def _stop_model_training(self, model_key):
        """Stoppe Modell Training"""
        LOGGERS['gui'].info(f"Stoppe {model_key} Training")
        if model_key in self.progress_bars:
            self.progress_bars[model_key].stop()
        if f"{model_key}_status" in self.status_labels:
            self.status_labels[f"{model_key}_status"].config(text=f"{model_key.title()}: Gestoppt")

    def _clear_logs(self):
        """Lösche Logs"""
        self.main_log_text.delete(1.0, tk.END)
        LOGGERS['gui'].info("Logs gelöscht")

    def _on_closing(self):
        """Beim Schließen des Fensters"""
        LOGGERS['gui'].info("Dashboard wird geschlossen...")

        # Stoppe GUI Updates
        self.gui_stop_event.set()

        # Stoppe Trading System
        self.trading_system.stop()

        # Fenster schließen
        self.root.destroy()

    def run(self):
        """Starte Dashboard"""
        LOGGERS['gui'].info("🎨 Dashboard gestartet")
        self.root.mainloop()

    def destroy(self):
        """Zerstöre Dashboard"""
        try:
            self.gui_stop_event.set()
            if self.root:
                self.root.destroy()
        except Exception as e:
            LOGGERS['gui'].error(f"Dashboard destroy error: {e}")

# ============================================================================
# HAUPTFUNKTION & LAUNCHER
# ============================================================================

def main():
    """🚀 Hauptfunktion - Starte Ultimate Master Trading System"""

    print("=" * 80)
    print("🚀 ULTIMATE MASTER BITCOIN TRADING SYSTEM")
    print("=" * 80)
    print("🎯 DETAILREICHES TRADING TOOL MIT 3 HAUPTMODELLEN")
    print("✨ Modernes Dashboard | Live-Monitoring | System-Überwachung")
    print("🧠 LSTM Deep Learning | Champion Ensemble | Technical Analysis")
    print("=" * 80)

    try:
        # System initialisieren
        print("\n🔧 Initialisiere System...")
        trading_system = UltimateMasterTradingSystem()

        if not trading_system.initialize():
            print("❌ System-Initialisierung fehlgeschlagen!")
            return False

        print("✅ System erfolgreich initialisiert")

        # Kontinuierlichen Betrieb starten
        print("\n🚀 Starte kontinuierlichen Betrieb...")
        trading_system.start_continuous_operation()

        # Dashboard erstellen und starten
        print("\n🎨 Erstelle Dashboard...")
        dashboard = trading_system.create_dashboard()

        if not dashboard:
            print("❌ Dashboard-Erstellung fehlgeschlagen!")
            trading_system.stop()
            return False

        print("✅ Dashboard erstellt")
        print("\n" + "=" * 80)
        print("🎉 ULTIMATE MASTER TRADING SYSTEM GESTARTET!")
        print("📊 Dashboard läuft - Fenster nicht schließen für kontinuierlichen Betrieb")
        print("🔄 System läuft kontinuierlich und trainiert Modelle automatisch")
        print("📈 Live Bitcoin-Daten werden alle 30 Sekunden aktualisiert")
        print("=" * 80)

        # Dashboard starten (blockiert bis Fenster geschlossen wird)
        dashboard.run()

        print("\n🛑 System wird beendet...")
        return True

    except KeyboardInterrupt:
        print("\n\n🚨 Benutzer-Unterbrechung erkannt...")
        print("🧹 Führe sauberes Cleanup durch...")
        return True

    except Exception as e:
        print(f"\n❌ Kritischer Fehler: {e}")
        LOGGERS['system'].error(f"Critical error in main: {e}")
        traceback.print_exc()
        return False

    finally:
        print("🧹 Cleanup abgeschlossen")
        print("👋 Auf Wiedersehen!")

def run_system_diagnostics():
    """🔍 System-Diagnose für Troubleshooting"""

    print("🔍 SYSTEM-DIAGNOSE")
    print("=" * 50)

    # Python Version
    print(f"🐍 Python Version: {sys.version}")

    # Verfügbare Pakete prüfen
    packages = {
        'TensorFlow': TF_AVAILABLE,
        'XGBoost': XGBOOST_AVAILABLE,
        'TA-Lib': TALIB_AVAILABLE
    }

    print("\n📦 Paket-Verfügbarkeit:")
    for package, available in packages.items():
        status = "✅ Verfügbar" if available else "❌ Nicht verfügbar"
        print(f"  {package}: {status}")

    # System-Ressourcen
    print(f"\n💻 System-Ressourcen:")
    print(f"  CPU Kerne: {psutil.cpu_count()}")
    print(f"  RAM Total: {psutil.virtual_memory().total / (1024**3):.1f} GB")
    print(f"  RAM Verfügbar: {psutil.virtual_memory().available / (1024**3):.1f} GB")

    # GPU Check
    try:
        import GPUtil
        gpus = GPUtil.getGPUs()
        if gpus:
            print(f"  GPU: {gpus[0].name} ({gpus[0].memoryTotal}MB)")
        else:
            print("  GPU: Keine NVIDIA GPU gefunden")
    except ImportError:
        print("  GPU: GPUtil nicht verfügbar")

    # Internet-Verbindung testen
    print(f"\n🌐 Netzwerk-Test:")
    try:
        response = requests.get('https://api.binance.com/api/v3/ping', timeout=5)
        if response.status_code == 200:
            print("  Binance API: ✅ Erreichbar")
        else:
            print("  Binance API: ❌ Fehler")
    except Exception as e:
        print(f"  Binance API: ❌ Fehler - {e}")

    # Daten-Test
    print(f"\n📊 Daten-Test:")
    try:
        data_provider = RealTimeDataProvider()
        current_price = data_provider.get_current_price()
        if current_price > 0:
            print(f"  Bitcoin Preis: ✅ ${current_price:,.2f}")
        else:
            print("  Bitcoin Preis: ❌ Nicht verfügbar")

        historical_data = data_provider.get_historical_data(hours=1)
        if not historical_data.empty:
            print(f"  Historische Daten: ✅ {len(historical_data)} Datenpunkte")
        else:
            print("  Historische Daten: ❌ Nicht verfügbar")

    except Exception as e:
        print(f"  Daten-Test: ❌ Fehler - {e}")

    print("\n" + "=" * 50)
    print("🔍 Diagnose abgeschlossen")

if __name__ == "__main__":
    # Kommandozeilen-Argumente
    if len(sys.argv) > 1:
        if sys.argv[1] == "--diagnostics" or sys.argv[1] == "-d":
            run_system_diagnostics()
            sys.exit(0)
        elif sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("🚀 ULTIMATE MASTER BITCOIN TRADING SYSTEM")
            print("\nVerwendung:")
            print("  python ULTIMATE_MASTER_TRADING_SYSTEM.py          # Starte System")
            print("  python ULTIMATE_MASTER_TRADING_SYSTEM.py -d       # System-Diagnose")
            print("  python ULTIMATE_MASTER_TRADING_SYSTEM.py -h       # Diese Hilfe")
            print("\nFeatures:")
            print("  🧠 LSTM Deep Learning Modell")
            print("  🏆 Champion Ensemble (RF + XGBoost + SVM)")
            print("  📈 Technical Analysis Master (50+ Indikatoren)")
            print("  🎨 Modernes Dashboard mit Live-Monitoring")
            print("  🔍 System-Monitoring (CPU/RAM/GPU)")
            print("  📊 Interaktive Charts mit Crosshair & Zoom")
            print("  🔄 Kontinuierliches Training & Updates")
            print("  🧹 Robustes Cleanup-System")
            sys.exit(0)

    # Hauptprogramm starten
    success = main()
    sys.exit(0 if success else 1)

# ============================================================================
# ZUSÄTZLICHE UTILITY FUNKTIONEN
# ============================================================================

def create_desktop_shortcut():
    """Erstelle Desktop-Verknüpfung (Windows)"""
    if os.name == 'nt':  # Windows
        try:
            import winshell
            from win32com.client import Dispatch

            desktop = winshell.desktop()
            path = os.path.join(desktop, "Ultimate Master Trading System.lnk")
            target = os.path.abspath(__file__)
            wDir = os.path.dirname(target)

            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = target
            shortcut.save()

            print(f"✅ Desktop-Verknüpfung erstellt: {path}")
            return True

        except ImportError:
            print("❌ winshell oder pywin32 nicht verfügbar für Desktop-Verknüpfung")
            return False
        except Exception as e:
            print(f"❌ Fehler beim Erstellen der Desktop-Verknüpfung: {e}")
            return False
    else:
        print("❌ Desktop-Verknüpfung nur unter Windows unterstützt")
        return False

def install_requirements():
    """Installiere fehlende Pakete"""
    required_packages = [
        'numpy', 'pandas', 'matplotlib', 'scikit-learn',
        'requests', 'yfinance', 'psutil', 'joblib'
    ]

    optional_packages = [
        'tensorflow', 'xgboost', 'talib', 'GPUtil'
    ]

    print("📦 Prüfe erforderliche Pakete...")

    missing_required = []
    missing_optional = []

    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_required.append(package)
            print(f"  ❌ {package} (erforderlich)")

    for package in optional_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_optional.append(package)
            print(f"  ⚠️ {package} (optional)")

    if missing_required:
        print(f"\n❌ Fehlende erforderliche Pakete: {', '.join(missing_required)}")
        print("Installiere mit: pip install " + " ".join(missing_required))
        return False

    if missing_optional:
        print(f"\n⚠️ Fehlende optionale Pakete: {', '.join(missing_optional)}")
        print("Für volle Funktionalität installiere: pip install " + " ".join(missing_optional))

    print("\n✅ Alle erforderlichen Pakete verfügbar")
    return True

# ============================================================================
# SCRIPT INFORMATIONEN
# ============================================================================

__version__ = "1.0.0"
__author__ = "Ultimate Master Trading System"
__description__ = """
🚀 ULTIMATE MASTER BITCOIN TRADING SYSTEM

Ein detailreiches Trading Tool mit 3 Hauptprognosemodellen:
- 🧠 LSTM Deep Learning (Neuronale Netze)
- 🏆 Champion Ensemble (Random Forest + XGBoost + SVM)
- 📈 Technical Analysis Master (50+ Indikatoren)

Features:
✅ Modernes Dashboard mit Live-Monitoring
✅ System-Monitoring (CPU/RAM/GPU)
✅ Kontinuierliches Training mit Intervall-Updates
✅ Interaktive Charts mit Crosshair & Zoom
✅ Separate Tabs mit Start/Stop-Kontrollen
✅ Detaillierte Fehlerausgabe & Logging
✅ Clean Process System
✅ Plugin-Architektur für Erweiterungen

Das System sammelt kontinuierlich Bitcoin-Daten, trainiert die Modelle
automatisch und erstellt Prognosen für die nächsten 24-48 Stunden.
"""

print(f"""
{__description__}

Version: {__version__}
Autor: {__author__}

Starte mit: python {__file__}
Hilfe: python {__file__} --help
Diagnose: python {__file__} --diagnostics
""") if __name__ == "__main__" else None
