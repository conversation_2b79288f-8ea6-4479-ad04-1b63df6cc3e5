@echo off
title Ultimate Master Bitcoin Trading System - Launcher
color 0A

echo.
echo ================================================================================
echo                🚀 ULTIMATE MASTER BITCOIN TRADING SYSTEM 🚀
echo ================================================================================
echo.
echo 🎯 DETAILREICHES TRADING TOOL MIT 3 HAUPTMODELLEN
echo ✨ Modernes Dashboard ^| Live-Monitoring ^| System-Überwachung
echo 🧠 LSTM Deep Learning ^| Champion Ensemble ^| Technical Analysis
echo.
echo ================================================================================
echo.

REM Prüfe ob Python verfügbar ist
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python ist nicht installiert oder nicht im PATH verfügbar!
    echo.
    echo Bitte installiere Python 3.8+ von https://python.org
    echo Stelle sicher, dass Python zum PATH hinzugefügt wird.
    echo.
    pause
    exit /b 1
)

echo ✅ Python gefunden
python --version

REM Prüfe ob das Hauptscript existiert
if not exist "ULTIMATE_MASTER_TRADING_SYSTEM.py" (
    echo.
    echo ❌ ULTIMATE_MASTER_TRADING_SYSTEM.py nicht gefunden!
    echo Stelle sicher, dass sich die Datei im gleichen Verzeichnis befindet.
    echo.
    pause
    exit /b 1
)

echo ✅ Hauptscript gefunden
echo.

REM Menü anzeigen
:MENU
echo ================================================================================
echo                              🎮 HAUPTMENÜ
echo ================================================================================
echo.
echo [1] 🚀 Trading System starten
echo [2] 🔍 System-Diagnose ausführen
echo [3] 📦 Pakete installieren/prüfen
echo [4] 🖥️ Desktop-Verknüpfung erstellen
echo [5] 📋 Hilfe anzeigen
echo [6] ❌ Beenden
echo.
set /p choice="Wähle eine Option (1-6): "

if "%choice%"=="1" goto START_SYSTEM
if "%choice%"=="2" goto DIAGNOSTICS
if "%choice%"=="3" goto INSTALL_PACKAGES
if "%choice%"=="4" goto CREATE_SHORTCUT
if "%choice%"=="5" goto SHOW_HELP
if "%choice%"=="6" goto EXIT

echo ❌ Ungültige Auswahl. Bitte wähle 1-6.
echo.
goto MENU

:START_SYSTEM
echo.
echo 🚀 Starte Ultimate Master Trading System...
echo ================================================================================
echo.
echo 📊 Das Dashboard wird geöffnet - bitte warten...
echo 🔄 System läuft kontinuierlich und trainiert Modelle automatisch
echo 📈 Live Bitcoin-Daten werden alle 30 Sekunden aktualisiert
echo.
echo ⚠️ WICHTIG: Schließe dieses Fenster NICHT während das System läuft!
echo ⚠️ Verwende das Dashboard-Fenster zum Beenden des Systems.
echo.
echo ================================================================================
echo.

python "ULTIMATE_MASTER_TRADING_SYSTEM.py"

if errorlevel 1 (
    echo.
    echo ❌ System wurde mit Fehler beendet!
    echo Prüfe die Logs für weitere Informationen.
) else (
    echo.
    echo ✅ System wurde erfolgreich beendet.
)

echo.
pause
goto MENU

:DIAGNOSTICS
echo.
echo 🔍 Führe System-Diagnose aus...
echo ================================================================================
echo.

python "ULTIMATE_MASTER_TRADING_SYSTEM.py" --diagnostics

echo.
echo ================================================================================
echo 🔍 Diagnose abgeschlossen
echo.
pause
goto MENU

:INSTALL_PACKAGES
echo.
echo 📦 Installiere/Prüfe erforderliche Pakete...
echo ================================================================================
echo.

echo 🔄 Installiere Basis-Pakete...
pip install numpy pandas matplotlib scikit-learn requests yfinance psutil joblib

echo.
echo 🔄 Installiere optionale Pakete (kann fehlschlagen - das ist normal)...
pip install tensorflow xgboost talib GPUtil

echo.
echo ================================================================================
echo 📦 Paket-Installation abgeschlossen
echo.
echo ℹ️ Einige optionale Pakete könnten fehlgeschlagen sein - das ist normal.
echo ℹ️ Das System funktioniert auch ohne diese Pakete (mit reduzierter Funktionalität).
echo.
pause
goto MENU

:CREATE_SHORTCUT
echo.
echo 🖥️ Erstelle Desktop-Verknüpfung...
echo ================================================================================
echo.

REM Erstelle einfache Batch-Datei auf Desktop
set "desktop=%USERPROFILE%\Desktop"
set "shortcut=%desktop%\Ultimate Master Trading System.bat"

echo @echo off > "%shortcut%"
echo cd /d "%~dp0" >> "%shortcut%"
echo call "ULTIMATE_MASTER_LAUNCHER.bat" >> "%shortcut%"

if exist "%shortcut%" (
    echo ✅ Desktop-Verknüpfung erstellt: %shortcut%
) else (
    echo ❌ Fehler beim Erstellen der Desktop-Verknüpfung
)

echo.
pause
goto MENU

:SHOW_HELP
echo.
echo 📋 HILFE - Ultimate Master Bitcoin Trading System
echo ================================================================================
echo.
echo 🎯 SYSTEM-ÜBERSICHT:
echo    Das Ultimate Master Trading System ist ein detailreiches Bitcoin Trading Tool
echo    mit 3 verschiedenen Prognosemodellen und modernem Dashboard.
echo.
echo 🧠 PROGNOSEMODELLE:
echo    1. LSTM Deep Learning - Neuronale Netze für komplexe Muster
echo    2. Champion Ensemble - Kombination aus Random Forest, XGBoost und SVM
echo    3. Technical Analysis Master - 50+ technische Indikatoren
echo.
echo 🎨 DASHBOARD-FEATURES:
echo    - Live Bitcoin-Preis Updates
echo    - Interaktive Charts mit Crosshair und Zoom
echo    - Separate Tabs für jedes Modell
echo    - System-Monitoring (CPU/RAM/GPU)
echo    - Start/Stop-Kontrollen für Training
echo    - Detaillierte Logs und Fehlerausgabe
echo.
echo ⚙️ SYSTEM-ANFORDERUNGEN:
echo    - Python 3.8 oder höher
echo    - Internetverbindung für Bitcoin-Daten
echo    - Mindestens 4GB RAM empfohlen
echo    - Optional: NVIDIA GPU für LSTM-Training
echo.
echo 🔧 FEHLERBEHEBUNG:
echo    1. Führe System-Diagnose aus (Option 2)
echo    2. Installiere fehlende Pakete (Option 3)
echo    3. Prüfe Internetverbindung
echo    4. Prüfe Python-Installation
echo.
echo 📞 SUPPORT:
echo    Bei Problemen prüfe die Log-Dateien im 'logs' Ordner.
echo.
echo ================================================================================
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 Auf Wiedersehen!
echo.
echo Vielen Dank für die Nutzung des Ultimate Master Trading Systems.
echo.
pause
exit /b 0
