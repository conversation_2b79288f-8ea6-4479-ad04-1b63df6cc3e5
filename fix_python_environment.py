#!/usr/bin/env python3
"""
🔧 PYTHON ENVIRONMENT FIX SCRIPT 🔧
==================================
Behebt gelbe Rufzeichen und Warnungen in der Python-Umgebung
"""

import os
import sys
import subprocess

def fix_tensorflow_warnings():
    """🔧 TensorFlow Warnungen unterdrücken"""
    print("🔧 Fixing TensorFlow warnings...")
    
    # Environment Variables setzen
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
    
    print("✅ TensorFlow warnings suppressed")

def check_python_environment():
    """🔍 Python-Umgebung überprüfen"""
    print("🔍 Checking Python environment...")
    
    print(f"✅ Python Version: {sys.version}")
    print(f"✅ Python Path: {sys.executable}")
    print(f"✅ Virtual Env: {sys.prefix}")
    
    # Wichtige Pakete testen
    try:
        import numpy
        print(f"✅ NumPy: {numpy.__version__}")
    except ImportError:
        print("❌ NumPy not found")
    
    try:
        import pandas
        print(f"✅ Pandas: {pandas.__version__}")
    except ImportError:
        print("❌ Pandas not found")
    
    try:
        import sklearn
        print(f"✅ Scikit-learn: {sklearn.__version__}")
    except ImportError:
        print("❌ Scikit-learn not found")
    
    try:
        import matplotlib
        print(f"✅ Matplotlib: {matplotlib.__version__}")
    except ImportError:
        print("❌ Matplotlib not found")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow: {tf.__version__}")
    except ImportError:
        print("❌ TensorFlow not found")

def fix_pip_cache():
    """🧹 Pip Cache leeren"""
    print("🧹 Clearing pip cache...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "cache", "purge"], 
                      capture_output=True, text=True)
        print("✅ Pip cache cleared")
    except Exception as e:
        print(f"⚠️ Could not clear pip cache: {e}")

def create_environment_script():
    """📝 Environment-Script erstellen"""
    print("📝 Creating environment setup script...")
    
    script_content = """@echo off
REM Python Environment Setup
set TF_CPP_MIN_LOG_LEVEL=2
set TF_ENABLE_ONEDNN_OPTS=0
set PYTHONPATH=%CD%
echo ✅ Environment variables set
"""
    
    with open("setup_environment.bat", "w") as f:
        f.write(script_content)
    
    print("✅ Created setup_environment.bat")

def main():
    """🚀 Hauptfunktion"""
    print("🔧 PYTHON ENVIRONMENT FIX")
    print("=" * 40)
    
    fix_tensorflow_warnings()
    check_python_environment()
    fix_pip_cache()
    create_environment_script()
    
    print("\n🎯 ENVIRONMENT FIX COMPLETE!")
    print("=" * 40)
    print("✅ TensorFlow warnings suppressed")
    print("✅ Environment checked")
    print("✅ Pip cache cleared")
    print("✅ Setup script created")
    print("\n💡 NEXT STEPS:")
    print("1. Restart your IDE/Editor")
    print("2. Run setup_environment.bat")
    print("3. Select correct Python interpreter")

if __name__ == "__main__":
    main()
